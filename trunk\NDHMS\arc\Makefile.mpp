# Makefile 

all:
	(make -f Makefile.comm BASIC)

BASIC:
	make -C MPP
	make -C IO
	make -C utils
	make -C OrchestratorLayer
	make -C Routing/Overland
	make -C Routing/Subsurface
	make -C Routing/Reservoirs
	make -C Data_Rec
	make -C Debug_Utilities
	make -C Routing
	make -C HYDRO_drv

clean:
	(cd IO; make -f Makefile clean)
	(cd OrchestratorLayer; make -f Makefile clean)
	(cd utils     ; make -f Makefile clean)
	make -C Routing/Overland clean
	make -C Routing/Subsurface clean
	make -C Routing/Reservoirs clean
	(cd Data_Rec; make -f Makefile clean)
	(cd HYDRO_drv; make -f Makefile clean)
	(cd MPP; make -f Makefile clean)
	make -C Debug_Utilities/ clean
	(cd Routing;    make -f Makefile clean)
	(rm -f lib/*.a */*.mod */*.o CPL/*/*.o CPL/*/*.mod)
