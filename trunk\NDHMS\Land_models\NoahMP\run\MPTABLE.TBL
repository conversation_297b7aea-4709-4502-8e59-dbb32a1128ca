&noahmp_usgs_veg_categories
 VEG_DATASET_DESCRIPTION = "USGS"
 NVEG = 27
/
&noahmp_usgs_parameters
 ! NVEG = 27
 !  1: Urban and Built-Up Land
 !  2: Dryland Cropland and Pasture
 !  3: Irrigated Cropland and Pasture
 !  4: Mixed Dryland/Irrigated Cropland and Pasture
 !  5: Cropland/Grassland Mosaic
 !  6: Cropland/Woodland Mosaic
 !  7: Grassland
 !  8: Shrubland
 !  9: Mixed Shrubland/Grassland
 ! 10: Savanna
 ! 11: Deciduous Broadleaf Forest
 ! 12: Deciduous Needleleaf Forest
 ! 13: Evergreen Broadleaf Forest
 ! 14: Evergreen Needleleaf Forest
 ! 15: Mixed Forest
 ! 16: Water Bodies
 ! 17: Herbaceous Wetland
 ! 18: Wooded Wetland
 ! 19: Barren or Sparsely Vegetated
 ! 20: Herbaceous Tundra
 ! 21: Wooded Tundra
 ! 22: Mixed Tundra
 ! 23: Bare Ground Tundra
 ! 24: Snow or Ice
 ! 25: Playa
 ! 26: Lava
 ! 27: White Sand

 ISURBAN                   =  1
 ISWATER                   = 16
 ISBARREN                  = 19
 ISICE                     = 24
 ISCROP                    =  2
 EBLFOREST                 = 13
 NATURAL                   =  5
 LOW_DENSITY_RESIDENTIAL   = 31
 HIGH_DENSITY_RESIDENTIAL  = 32
 HIGH_INTENSITY_INDUSTRIAL = 33

 !---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 !          1      2      3      4      5      6      7      8      9      10     11     12     13     14     15     16     17     18     19     20     21     22     23     24     25     26    27
 !---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 CH2OP =   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,   0.1,
 DLEAF =  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,  0.04,
 Z0MVT =  1.00,  0.15,  0.15,  0.15,  0.14,  0.50,  0.12,  0.06,  0.09,  0.50,  0.80,  0.85,  1.10,  1.09,  0.80,  0.00,  0.12,  0.50,  0.00,  0.10,  0.30,  0.20,  0.03,  0.00,  0.01,  0.00,  0.00,
 HVT   =  15.0,  2.00,  2.00,  2.00,  1.50,  8.00,  1.00,  1.10,  1.10,  10.0,  16.0,  18.0,  20.0,  20.0,  16.0,  0.00,  0.50,  10.0,  0.00,  0.50,  4.00,  2.00,  0.50,  0.00,  0.10,  0.00,  0.00,
 HVB   =  1.00,  0.10,  0.10,  0.10,  0.10,  0.15,  0.05,  0.10,  0.10,  0.10,  11.5,  7.00,  8.00,  8.50,  10.0,  0.00,  0.05,  0.10,  0.00,  0.10,  0.10,  0.10,  0.10,  0.00,  0.10,  0.00,  0.00,
 DEN   =  0.01,  25.0,  25.0,  25.0,  25.0,  25.0,  100.,  10.0,  10.0,  0.02,  0.10,  0.28,  0.02,  0.28,  0.10,  0.01,  10.0,  0.10,  0.01,  1.00,  1.00,  1.00,  1.00,  0.00,  0.01,  0.01,  0.01,
 RC    =  1.00,  0.08,  0.08,  0.08,  0.08,  0.08,  0.03,  0.12,  0.12,  3.00,  1.40,  1.20,  3.60,  1.20,  1.40,  0.01,  0.10,  1.40,  0.01,  0.30,  0.30,  0.30,  0.30,  0.00,  0.01,  0.01,  0.01,
 MFSNO =  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,  2.50,

 ! Row 1:  Vis
 ! Row 2:  Near IR
 RHOL_VIS=0.00,  0.11,  0.11,  0.11,  0.11,  0.11,  0.11,  0.07,  0.10,  0.10,  0.10,  0.07,  0.10,  0.07,  0.10,  0.00,  0.11,  0.10,  0.00,  0.10,  0.10,  0.10,  0.10,  0.00,  0.10,  0.00,  0.00,
 RHOL_NIR=0.00,  0.58,  0.58,  0.58,  0.58,  0.58,  0.58,  0.35,  0.45,  0.45,  0.45,  0.35,  0.45,  0.35,  0.45,  0.00,  0.58,  0.45,  0.00,  0.45,  0.45,  0.45,  0.45,  0.00,  0.45,  0.00,  0.00,

 ! Row 1:  Vis
 ! Row 2:  Near IR
 RHOS_VIS=0.00,  0.36,  0.36,  0.36,  0.36,  0.36,  0.36,  0.16,  0.16,  0.16,  0.16,  0.16,  0.16,  0.16,  0.16,  0.00,  0.36,  0.16,  0.00,  0.16,  0.16,  0.16,  0.16,  0.00,  0.16,  0.00,  0.00,
 RHOS_NIR=0.00,  0.58,  0.58,  0.58,  0.58,  0.58,  0.58,  0.39,  0.39,  0.39,  0.39,  0.39,  0.39,  0.39,  0.39,  0.00,  0.58,  0.39,  0.00,  0.39,  0.39,  0.39,  0.39,  0.00,  0.39,  0.00,  0.00,

 ! Row 1:  Vis
 ! Row 2:  Near IR
 TAUL_VIS=0.00,  0.07,  0.07,  0.07,  0.07,  0.07,  0.07,  0.05,  0.05,  0.05,  0.05,  0.05,  0.05,  0.05,  0.05,  0.00,  0.07,  0.05,  0.00,  0.05,  0.05,  0.05,  0.05,  0.00,  0.05,  0.00,  0.00,
 TAUL_NIR=0.00,  0.25,  0.25,  0.25,  0.25,  0.25,  0.25,  0.10,  0.10,  0.25,  0.25,  0.10,  0.25,  0.10,  0.25,  0.00,  0.25,  0.25,  0.00,  0.25,  0.25,  0.25,  0.25,  0.00,  0.25,  0.00,  0.00,

 ! Row 1:  Vis
 ! Row 2:  Near IR
 TAUS_VIS=0.00, 0.220, 0.220, 0.220, 0.220, 0.220, 0.220, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.000, 0.220, 0.001, 0.000, 0.220, 0.001, 0.001, 0.001, 0.000, 0.001, 0.000, 0.000,
 TAUS_NIR=0.00, 0.380, 0.380, 0.380, 0.380, 0.380, 0.380, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.000, 0.380, 0.001, 0.000, 0.380, 0.001, 0.001, 0.001, 0.000, 0.001, 0.000, 0.000,

 XL    = 0.000, -0.30, -0.30, -0.30, -0.30, -0.30, -0.30, 0.010, 0.250, 0.010, 0.250, 0.010, 0.010, 0.010, 0.250, 0.000, -0.30, 0.250, 0.000, -0.30, 0.250, 0.250, 0.250, 0.000, 0.250, 0.000, 0.000,
 CWPVT =  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,  0.18,
 C3PSN =   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,
 KC25  =  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0,
 AKC   =   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,   2.1,
 KO25  =  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,  3.E4,
 AKO   =   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,   1.2,
 AVCMX =   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,   2.4,
 AQE   =   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,   1.0,

 LTOVRC=   0.0,   1.2,   1.2,   1.2,   1.2,  1.30,  0.50,  0.65,  0.70,  0.65,  0.55,   0.2,  0.55,   0.5,   0.5,   0.0,   1.4,   1.4,   0.0,   1.2,   1.3,   1.4,   1.0,   0.0,   1.0,   0.0,   0.0,
 DILEFC=  0.00,  0.50,  0.50,  0.50,  0.35,  0.20,  0.20,  0.20,  0.50,  0.50,  0.60,  1.80,  0.50,  1.20,  0.80,  0.00,  0.40,  0.40,  0.00,  0.40,  0.30,  0.40,  0.30,  0.00,  0.30,  0.00,  0.00,
 DILEFW=  0.00,  0.20,  0.20,  0.20,  0.20,  0.20,  0.10,  0.20,  0.20,  0.50,  0.20,  0.20,  4.00,  0.20,  0.20,  0.00,  0.20,  0.20,  0.00,  0.20,  0.20,  0.20,  0.20,  0.00,  0.20,  0.00,  0.00,
 RMF25 =  0.00,  1.00,  1.40,  1.45,  1.45,  1.45,  1.80,  0.26,  0.26,  0.80,  3.00,  4.00,  0.65,  3.00,  3.00,  0.00,  3.20,  3.20,  0.00,  3.20,  3.00,  3.00,  3.00,  0.00,  3.00,  0.00,  0.00,
 SLA   =    60,    80,    80,    80,    80,    80,    60,    60,    60,    50,    80,    80,    80,    80,    80,     0,    80,    80,     0,    80,    80,    80,    80,     0,    80,     0,     0,
 FRAGR =  0.00,  0.20,  0.20,  0.20,  0.20,  0.20,  0.20,  0.20,  0.20,  0.20,  0.20,  0.10,  0.20,  0.10,  0.10,  0.00,  0.10,  0.10,  0.10,  0.10,  0.10,  0.10,  0.10,  0.00,  0.10,  0.00,  0.00,
 TMIN  =     0,   273,   273,   273,   273,   273,   273,   273,   273,   273,   273,   268,   273,   265,   268,     0,   268,   268,     0,   268,   268,   268,   268,     0,   268,     0,     0,
 VCMX25=  0.00,  80.0,  80.0,  80.0,  60.0,  70.0,  40.0,  40.0,  40.0,  40.0,  60.0,  60.0,  60.0,  50.0,  55.0,  0.00,  50.0,  50.0,  0.00,  50.0,  50.0,  50.0,  50.0,  0.00,  50.0,  0.00,  0.00,
 TDLEF =   278,   278,   278,   278,   278,   278,   278,   278,   278,   278,   278,   268,   278,   278,   268,     0,   268,   268,     0,   268,   268,   268,   268,     0,   268,     0,     0,
 BP    = 1.E15,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3, 1.E15,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3,  2.E3, 1.E15,  2.E3, 1.E15, 1.E15,
 MP    =    9.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,    6.,    9.,    6.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,    9.,
 QE25  =    0.,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.00,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.06,  0.00,  0.06,  0.00,  0.00,
 RMS25 =  0.00,  0.10,  0.10,  0.10,  0.10,  0.10,  0.10,  0.10,  0.10,  0.32,  0.10,  0.64,  0.30,  0.90,  0.80,  0.00,  0.10,  0.10,  0.00,  0.10,  0.10,  0.10,  0.00,  0.00,  0.00,  0.00,  0.00,
 RMR25 =  0.00,  0.00,  0.00,  0.00,  0.00,  0.00,  1.20,  0.00,  0.00,  0.01,  0.01,  0.05,  0.05,  0.36,  0.03,  0.00,  0.00,  0.00,  0.00,  2.11,  2.11,  2.11,  0.00,  0.00,  0.00,  0.00,  0.00,
 ARM   =   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,   2.0,
 FOLNMX=  0.00,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,  0.00,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,   1.5,  0.00,   1.5,  0.00,  0.00,
 WDPOOL=  0.00,  0.00,  0.00,  0.00,  0.00,  0.00,  0.00,  1.00,  1.00,  1.00,  1.00,  1.00,  1.00,  1.00,  1.00,  0.00,  0.00,  1.00,  0.00,  0.00,  1.00,  1.00,  0.00,  0.00,  0.00,  0.00,  0.00,
 WRRAT =  0.00,  0.00,  0.00,  0.00,  0.00,  0.00,  0.00,  3.00,  3.00,  3.00,  30.0,  30.0,  30.0,  30.0,  30.0,  0.00,  0.00,  30.0,  0.00,  0.00,  3.00,  3.00,  0.00,  0.00,  0.00,  0.00,  0.00,
 MRP   =  0.00,  0.23,  0.23,  0.23,  0.23,  0.23,  0.17,  0.19,  0.19,  0.40,  0.40,  0.37,  0.23,  0.37,  0.30,  0.00,  0.17,  0.40,  0.00,  0.17,  0.23,  0.20,  0.00,  0.00,  0.20,  0.00,  0.00,
 NROOT =     1,     3,     3,     3,     3,     3,     3,     3,     3,     3,     4,     4,     4,     4,     4,     0,     2,     2,     1,     3,     3,     3,     2,     1,     1,     0,     0,
 RGL   = 999.0, 100.0, 100.0, 100.0, 100.0,  65.0, 100.0, 100.0, 100.0,  65.0,  30.0,  30.0,  30.0,  30.0,  30.0,  30.0, 100.0,  30.0, 999.0, 100.0, 100.0, 100.0, 100.0, 999.0, 100.0, 999.0, 999.0,
 RS    = 200.0,  40.0,  40.0,  40.0,  40.0,  70.0,  40.0, 300.0, 170.0,  70.0, 100.0, 150.0, 150.0, 125.0, 125.0, 100.0,  40.0, 100.0, 999.0, 150.0, 150.0, 150.0, 200.0, 999.0,  40.0, 999.0, 999.0,
 HS    = 999.0, 36.25, 36.25, 36.25, 36.25, 44.14, 36.35, 42.00, 39.18, 54.53, 54.53, 47.35, 41.69, 47.35, 51.93, 51.75, 60.00, 51.93, 999.0, 42.00, 42.00, 42.00, 42.00, 999.0, 36.25, 999.0, 999.0,
 TOPT  = 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0, 298.0,
 RSMAX = 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000., 5000.,

! Monthly values, one row for each month:
 SAI_JAN = 0.0,   0.3,   0.3,   0.3,   0.3,   0.3,   0.3,   0.2,   0.2,   0.3,   0.4,   0.3,   0.5,   0.4,   0.4,   0.0,   0.2,   0.3,   0.0,   0.1,   0.2,   0.1,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_FEB = 0.0,   0.3,   0.3,   0.3,   0.3,   0.3,   0.3,   0.2,   0.2,   0.3,   0.4,   0.3,   0.5,   0.4,   0.4,   0.0,   0.2,   0.3,   0.0,   0.1,   0.2,   0.1,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_MAR = 0.0,   0.3,   0.3,   0.3,   0.3,   0.3,   0.3,   0.2,   0.2,   0.3,   0.4,   0.3,   0.5,   0.4,   0.4,   0.0,   0.2,   0.3,   0.0,   0.1,   0.2,   0.1,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_APR = 0.0,   0.3,   0.3,   0.3,   0.3,   0.3,   0.3,   0.2,   0.2,   0.3,   0.4,   0.4,   0.5,   0.3,   0.4,   0.0,   0.2,   0.3,   0.0,   0.1,   0.2,   0.1,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_MAY = 0.0,   0.2,   0.2,   0.2,   0.3,   0.3,   0.3,   0.2,   0.2,   0.3,   0.4,   0.4,   0.5,   0.4,   0.4,   0.0,   0.3,   0.3,   0.0,   0.1,   0.2,   0.1,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_JUN = 0.0,   0.3,   0.3,   0.3,   0.4,   0.4,   0.4,   0.2,   0.3,   0.4,   0.4,   0.7,   0.5,   0.5,   0.4,   0.0,   0.4,   0.4,   0.0,   0.2,   0.2,   0.2,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_JUL = 0.0,   0.4,   0.4,   0.4,   0.6,   0.6,   0.8,   0.4,   0.6,   0.8,   0.9,   1.3,   0.5,   0.5,   0.7,   0.0,   0.6,   0.6,   0.0,   0.4,   0.4,   0.4,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_AUG = 0.0,   0.5,   0.5,   0.5,   0.9,   0.9,   1.3,   0.6,   0.9,   1.2,   1.2,   1.2,   0.5,   0.6,   0.8,   0.0,   0.9,   0.9,   0.0,   0.6,   0.6,   0.6,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_SEP = 0.0,   0.4,   0.4,   0.4,   0.7,   1.0,   1.1,   0.8,   1.0,   1.3,   1.6,   1.0,   0.5,   0.6,   1.0,   0.0,   0.7,   1.0,   0.0,   0.7,   0.8,   0.7,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_OCT = 0.0,   0.3,   0.3,   0.3,   0.3,   0.8,   0.4,   0.7,   0.6,   0.7,   1.4,   0.8,   0.5,   0.7,   1.0,   0.0,   0.3,   0.8,   0.0,   0.5,   0.7,   0.5,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_NOV = 0.0,   0.3,   0.3,   0.3,   0.3,   0.4,   0.4,   0.3,   0.3,   0.4,   0.6,   0.6,   0.5,   0.6,   0.5,   0.0,   0.3,   0.4,   0.0,   0.3,   0.3,   0.3,   0.0,   0.0,   0.0,   0.0,   0.0,
 SAI_DEC = 0.0,   0.3,   0.3,   0.3,   0.3,   0.3,   0.4,   0.2,   0.3,   0.4,   0.4,   0.5,   0.5,   0.5,   0.4,   0.0,   0.3,   0.4,   0.0,   0.2,   0.2,   0.2,   0.0,   0.0,   0.0,   0.0,   0.0,

 LAI_JAN = 0.0,   0.0,   0.0,   0.0,   0.2,   0.0,   0.4,   0.0,   0.2,   0.3,   0.0,   0.0,   4.5,   4.0,   2.0,   0.0,   0.2,   0.2,   0.0,   0.2,   1.0,   0.6,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_FEB = 0.0,   0.0,   0.0,   0.0,   0.3,   0.0,   0.5,   0.0,   0.3,   0.3,   0.0,   0.0,   4.5,   4.0,   2.0,   0.0,   0.3,   0.3,   0.0,   0.3,   1.0,   0.6,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_MAR = 0.0,   0.0,   0.0,   0.0,   0.3,   0.2,   0.6,   0.2,   0.4,   0.5,   0.3,   0.0,   4.5,   4.0,   2.2,   0.0,   0.3,   0.3,   0.0,   0.3,   1.1,   0.7,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_APR = 0.0,   0.0,   0.0,   0.0,   0.4,   0.6,   0.7,   0.6,   0.7,   0.8,   1.2,   0.6,   4.5,   4.0,   2.6,   0.0,   0.4,   0.6,   0.0,   0.4,   1.3,   0.8,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_MAY = 0.0,   1.0,   1.0,   1.0,   1.1,   2.0,   1.2,   1.5,   1.4,   1.8,   3.0,   1.2,   4.5,   4.0,   3.5,   0.0,   1.1,   2.0,   0.0,   0.6,   1.7,   1.2,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_JUN = 0.0,   2.0,   2.0,   2.0,   2.5,   3.3,   3.0,   2.3,   2.6,   3.6,   4.7,   2.0,   4.5,   4.0,   4.3,   0.0,   2.5,   3.3,   0.0,   1.5,   2.1,   1.8,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_JUL = 0.0,   3.0,   3.0,   3.0,   3.2,   3.7,   3.5,   2.3,   2.9,   3.8,   4.5,   2.6,   4.5,   4.0,   4.3,   0.0,   3.2,   3.7,   0.0,   1.7,   2.1,   1.8,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_AUG = 0.0,   3.0,   3.0,   3.0,   2.2,   3.2,   1.5,   1.7,   1.6,   2.1,   3.4,   1.7,   4.5,   4.0,   3.7,   0.0,   2.2,   3.2,   0.0,   0.8,   1.8,   1.3,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_SEP = 0.0,   1.5,   1.5,   1.5,   1.1,   1.3,   0.7,   0.6,   0.7,   0.9,   1.2,   1.0,   4.5,   4.0,   2.6,   0.0,   1.1,   1.3,   0.0,   0.4,   1.3,   0.8,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_OCT = 0.0,   0.0,   0.0,   0.0,   0.3,   0.2,   0.6,   0.2,   0.4,   0.5,   0.3,   0.5,   4.5,   4.0,   2.2,   0.0,   0.3,   0.3,   0.0,   0.3,   1.1,   0.7,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_NOV = 0.0,   0.0,   0.0,   0.0,   0.3,   0.0,   0.5,   0.0,   0.3,   0.3,   0.0,   0.2,   4.5,   4.0,   2.0,   0.0,   0.3,   0.3,   0.0,   0.2,   1.0,   0.6,   0.0,   0.0,   0.0,   0.0,   0.0,
 LAI_DEC = 0.0,   0.0,   0.0,   0.0,   0.2,   0.0,   0.4,   0.0,   0.2,   0.3,   0.0,   0.0,   4.5,   4.0,   2.0,   0.0,   0.2,   0.2,   0.0,   0.2,   1.0,   0.6,   0.0,   0.0,   0.0,   0.0,   0.0,

 SLAREA=0.0228,0.0200,0.0200,0.0295,0.0223,0.0277,0.0060,0.0227,0.0188,0.0236,0.0258,0.0200,0.0200,0.0090,0.0223,0.0422,0.0390,  0.02,  0.02,  0.02,  0.02,  0.02,  0.02,  0.02,  0.02,  0.02,  0.02,

! Five types, one row for each type (BVOC currently not active).
 EPS1   = 41.87,  0.00,  0.00,  2.52,  0.04, 17.11,  0.02, 21.62,  0.11, 22.80, 46.86,  0.00,  0.00,  0.46, 30.98,  2.31,  1.63,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,
 EPS2   =  0.98,  0.00,  0.00,  0.16,  0.09,  0.28,  0.05,  0.92,  0.22,  0.59,  0.38,  0.00,  0.00,  3.34,  0.96,  1.47,  1.07,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,
 EPS3   =  1.82,  0.00,  0.00,  0.23,  0.05,  0.81,  0.03,  1.73,  1.26,  1.37,  1.84,  0.00,  0.00,  1.85,  1.84,  1.70,  1.21,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,
 EPS4   =   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,
 EPS5   =   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,   0.0,
/

&noahmp_modis_veg_categories
 VEG_DATASET_DESCRIPTION = "modified igbp modis noah"
 NVEG = 20
/

&noahmp_modis_parameters
! 1          'Evergreen Needleleaf Forest'                       -> USGS 14
! 2,         'Evergreen Broadleaf Forest'                        -> USGS 13
! 3,         'Deciduous Needleleaf Forest'                       -> USGS 12
! 4,         'Deciduous Broadleaf Forest'                        -> USGS 11
! 5,         'Mixed Forests'                                     -> USGS 15
! 6,         'Closed Shrublands'                                 -> USGS  8 "shrubland"
! 7,         'Open Shrublands'                                   -> USGS  9 "shrubland/grassland"
! 8,         'Woody Savannas'                                    -> USGS  8 "shrubland"
! 9,         'Savannas'                                          -> USGS 10
! 10,        'Grasslands'                                        -> USGS  7
! 11         'Permanent wetlands'                                -> avg of USGS 17 and 18 (herb. wooded wetland)
! 12,        'Croplands'                                         -> USGS  2 "dryland cropland"
! 13,        'Urban and Built-Up'                                -> USGS  1
! 14         'cropland/natural vegetation mosaic'                -> USGS  5 "cropland/grassland"
! 15,        'Snow and Ice'                                      -> USGS 24
! 16,        'Barren or Sparsely Vegetated'                      -> USGS 19
! 17,        'Water'                                             -> USGS 16
! 18,        'Wooded Tundra'                                     -> USGS 21
! 19,        'Mixed Tundra'                                      -> USGS 22
! 20,        'Barren Tundra'                                     -> USGS 23

 ISURBAN                   = 13
 ISWATER                   = 17
 ISBARREN                  = 16
 ISICE                     = 15
 ISCROP                    = 12
 EBLFOREST                 =  2
 NATURAL                   = 14
 LOW_DENSITY_RESIDENTIAL   = 31
 HIGH_DENSITY_RESIDENTIAL  = 32
 HIGH_INTENSITY_INDUSTRIAL = 33

 !---------------------------------------------------------------------------------------------------------------------------------------------------------------------
 !          1       2       3       4       5       6       7       8       9      10      11      12      13      14      15      16      17      18      19      20
 !---------------------------------------------------------------------------------------------------------------------------------------------------------------------
 CH2OP =   0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,    0.1,
 DLEAF =  0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,   0.04,
 Z0MVT =  1.09,   1.10,   0.85,   0.80,   0.80,   0.20,   0.06,   0.60,   0.50,   0.12,   0.30,   0.15,   1.00,   0.14,   0.00,   0.00,   0.00,   0.30,   0.20,   0.03,
 HVT   =  20.0,   20.0,   18.0,   16.0,   16.0,   1.10,   1.10,   13.0,   10.0,   1.00,   5.00,   2.00,   15.0,   1.50,   0.00,   0.00,   0.00,   4.00,   2.00,   0.50,
 HVB   =  8.50,   8.00,   7.00,   11.5,   10.0,   0.10,   0.10,   0.10,   0.10,   0.05,   0.10,   0.10,   1.00,   0.10,   0.00,   0.00,   0.00,   0.30,   0.20,   0.10,
 DEN   =  0.28,   0.02,   0.28,   0.10,   0.10,   10.0,   10.0,   10.0,   0.02,   100.,   5.05,   25.0,   0.01,   25.0,   0.00,   0.01,   0.01,   1.00,   1.00,   1.00,
 RC    =  1.20,   3.60,   1.20,   1.40,   1.40,   0.12,   0.12,   0.12,   3.00,   0.03,   0.75,   0.08,   1.00,   0.08,   0.00,   0.01,   0.01,   0.30,   0.30,   0.30,
 MFSNO =  2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,   2.50,

 ! Row 1:  Vis
 ! Row 2:  Near IR
 RHOL_VIS=0.07,   0.10,   0.07,   0.10,   0.10,   0.07,   0.07,   0.07,   0.10,   0.11,  0.105,   0.11,   0.00,   0.11,   0.00,   0.00,   0.00,   0.10,   0.10,   0.10,
 RHOL_NIR=0.35,   0.45,   0.35,   0.45,   0.45,   0.35,   0.35,   0.35,   0.45,   0.58,  0.515,   0.58,   0.00,   0.58,   0.00,   0.00,   0.00,   0.45,   0.45,   0.45,

 ! Row 1:  Vis
 ! Row 2:  Near IR
 RHOS_VIS=0.16,   0.16,   0.16,   0.16,   0.16,   0.16,   0.16,   0.16,   0.16,   0.36,   0.26,   0.36,   0.00,   0.36,   0.00,   0.00,   0.00,   0.16,   0.16,   0.16,
 RHOS_NIR=0.39,   0.39,   0.39,   0.39,   0.39,   0.39,   0.39,   0.39,   0.39,   0.58,  0.485,   0.58,   0.00,   0.58,   0.00,   0.00,   0.00,   0.39,   0.39,   0.39,

 ! Row 1:  Vis
 ! Row 2:  Near IR
 TAUL_VIS=0.05,   0.05,   0.05,   0.05,   0.05,   0.05,   0.05,   0.05,   0.05,   0.07,   0.06,   0.07,   0.00,   0.07,   0.00,   0.00,   0.00,   0.05,   0.05,   0.05,
 TAUL_NIR=0.10,   0.25,   0.10,   0.25,   0.25,   0.10,   0.10,   0.10,   0.25,   0.25,   0.25,   0.25,   0.00,   0.25,   0.00,   0.00,   0.00,   0.25,   0.25,   0.25,

 ! Row 1:  Vis
 ! Row 2:  Near IR
 TAUS_VIS=0.001,  0.001,  0.001,  0.001,  0.001,  0.001,  0.001,  0.001,  0.001,  0.220, 0.1105,  0.220,  0.000,  0.220,  0.000,  0.000,  0.000,  0.001,  0.001,  0.001,
 TAUS_NIR=0.001,  0.001,  0.001,  0.001,  0.001,  0.001,  0.001,  0.001,  0.001,  0.380, 0.1905,  0.380,  0.000,  0.380,  0.000,  0.000,  0.000,  0.001,  0.001,  0.001,

 XL    = 0.010,  0.010,  0.010,  0.250,  0.250,  0.010,  0.010,  0.010,  0.010,  -0.30, -0.025,  -0.30,  0.000,  -0.30,  0.000,  0.000,  0.000,  0.250,  0.250,  0.250,
! CWPVT =   3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,    3.0,
 CWPVT =  0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,   0.18,
 C3PSN =   1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,
 KC25  =  30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,   30.0,
 AKC   =   2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,    2.1,
 KO25  =  3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,   3.E4,
 AKO   =   1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,    1.2,
 AVCMX =   2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,    2.4,
 AQE   =   1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,    1.0,

 LTOVRC=   0.5,   0.55,    0.2,   0.55,    0.5,   0.65,   0.65,   0.65,   0.65,   0.50,    1.4,    1.6,    0.0,    1.2,    0.0,    0.0,    0.0,    1.3,    1.4,    1.0,
 DILEFC=  1.20,   0.50,   1.80,   0.60,   0.80,   0.20,   0.20,   0.20,   0.50,   0.20,    0.4,   0.50,   0.00,   0.35,   0.00,   0.00,   0.00,   0.30,   0.40,   0.30,
 DILEFW=  0.20,   4.00,   0.20,   0.20,   0.20,   0.20,   0.20,   0.20,   0.50,   0.10,    0.2,   0.20,   0.00,   0.20,   0.00,   0.00,   0.00,   0.20,   0.20,   0.20,
 RMF25 =  3.00,   0.65,   4.00,   3.00,   3.00,   0.26,   0.26,   0.26,   0.80,   1.80,    3.2,   1.00,   0.00,   1.45,   0.00,   0.00,   0.00,   3.00,   3.00,   3.00,
 SLA   =    80,     80,     80,     80,     80,     60,     60,     60,     50,     60,     80,     80,     60,     80,      0,      0,      0,     80,     80,     80,
 FRAGR =  0.10,   0.20,   0.10,   0.20,   0.10,   0.20,   0.20,   0.20,   0.20,   0.20,    0.1,   0.20,   0.00,   0.20,   0.00,   0.10,   0.00,   0.10,   0.10,   0.10,
 TMIN  =   265,    273,    268,    273,    268,    273,    273,    273,    273,    273,    268,    273,      0,    273,      0,      0,      0,    268,    268,    268,
 VCMX25=  50.0,   60.0,   60.0,   60.0,   55.0,   40.0,   40.0,   40.0,   40.0,   40.0,   50.0,   80.0,   0.00,   60.0,   0.00,   0.00,   0.00,   50.0,   50.0,   50.0,
 TDLEF =   278,    278,    268,    278,    268,    278,    278,    278,    278,    278,    268,    278,    278,    278,      0,      0,      0,    268,    268,    268,
 BP    =  2.E3,   2.E3,   2.E3,   2.E3,   2.E3,   2.E3,   2.E3,   2.E3,   2.E3,   2.E3,   2.E3,   2.E3,  1.E15,   2.E3,  1.E15,   2.E3,  1.E15,   2.E3,   2.E3,   2.E3,
 MP    =    6.,     9.,     6.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,     9.,
 QE25  =  0.06,   0.06,   0.06,   0.06,   0.06,   0.06,   0.06,   0.06,   0.06,   0.06,   0.06,   0.06,   0.00,   0.06,   0.00,   0.06,   0.00,   0.06,   0.06,   0.06,
 RMS25 =  0.90,   0.30,   0.64,   0.10,   0.80,   0.10,   0.10,   0.10,   0.32,   0.10,   0.10,   0.10,   0.00,   0.10,   0.00,   0.00,   0.00,   0.10,   0.10,   0.00,
 RMR25 =  0.36,   0.05,   0.05,   0.01,   0.03,   0.00,   0.00,   0.00,   0.01,   1.20,    0.0,   0.00,   0.00,   0.00,   0.00,   0.00,   0.00,   2.11,   2.11,   0.00,
 ARM   =   2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,    2.0,
 FOLNMX=   1.5,    1.5,    1.5,    1.5,    1.5,    1.5,    1.5,    1.5,    1.5,    1.5,    1.5,    1.5,   0.00,    1.5,   0.00,    1.5,   0.00,    1.5,    1.5,    1.5,
 WDPOOL=  1.00,   1.00,   1.00,   1.00,   1.00,   1.00,   1.00,   1.00,   1.00,   0.00,    0.5,   0.00,   0.00,   0.00,   0.00,   0.00,   0.00,   1.00,   1.00,   0.00,
 WRRAT =  30.0,   30.0,   30.0,   30.0,   30.0,   3.00,   3.00,   3.00,   3.00,   0.00,   15.0,   0.00,   0.00,   0.00,   0.00,   0.00,   0.00,   3.00,   3.00,   0.00,
 MRP   =  0.37,   0.23,   0.37,   0.40,   0.30,   0.19,   0.19,   0.19,   0.40,   0.17,  0.285,   0.23,   0.00,   0.23,   0.00,   0.00,   0.00,   0.23,   0.20,   0.00,
 NROOT =     4,      4,      4,      4,      4,      3,      3,      3,      3,      3,      2,      3,      1,      3,      1,      1,      0,      3,      3,      2,     
 RGL   =  30.0,   30.0,   30.0,   30.0,   30.0,  100.0,  100.0,  100.0,   65.0,  100.0,   65.0,  100.0,  999.0,  100.0,  999.0,  999.0,   30.0,  100.0,  100.0,  100.0,
 RS    = 125.0,  150.0,  150.0,  100.0,  125.0,  300.0,  170.0,  300.0,   70.0,   40.0,   70.0,   40.0,  200.0,   40.0,  999.0,  999.0,  100.0,  150.0,  150.0,  200.0,
 HS    = 47.35,  41.69,  47.35,  54.53,  51.93,  42.00,  39.18,  42.00,  54.53,  36.35,  55.97,  36.25,  999.0,  36.25,  999.0,  999.0,  51.75,  42.00,  42.00,  42.00,
 TOPT  = 298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,  298.0,
 RSMAX = 5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,  5000.,

! Monthly values, one row for each month:
 SAI_JAN = 0.4,    0.5,    0.3,    0.4,    0.4,    0.3,    0.2,    0.4,    0.3,    0.3,    0.3,    0.3,    0.0,    0.3,    0.0,    0.0,    0.0,    0.2,    0.1,    0.0,
 SAI_FEB = 0.4,    0.5,    0.3,    0.4,    0.4,    0.3,    0.2,    0.4,    0.3,    0.3,    0.3,    0.3,    0.0,    0.3,    0.0,    0.0,    0.0,    0.2,    0.1,    0.0,
 SAI_MAR = 0.4,    0.5,    0.3,    0.4,    0.4,    0.3,    0.2,    0.4,    0.3,    0.3,    0.3,    0.3,    0.0,    0.3,    0.0,    0.0,    0.0,    0.2,    0.1,    0.0,
 SAI_APR = 0.3,    0.5,    0.4,    0.4,    0.4,    0.3,    0.2,    0.4,    0.3,    0.3,    0.3,    0.3,    0.0,    0.3,    0.0,    0.0,    0.0,    0.2,    0.1,    0.0,
 SAI_MAY = 0.4,    0.5,    0.4,    0.4,    0.4,    0.3,    0.2,    0.4,    0.3,    0.3,    0.3,    0.3,    0.0,    0.3,    0.0,    0.0,    0.0,    0.2,    0.1,    0.0,
 SAI_JUN = 0.5,    0.5,    0.7,    0.4,    0.4,    0.3,    0.2,    0.4,    0.4,    0.4,    0.4,    0.3,    0.0,    0.4,    0.0,    0.0,    0.0,    0.2,    0.2,    0.0,
 SAI_JUL = 0.5,    0.5,    1.3,    0.9,    0.7,    0.6,    0.4,    0.7,    0.8,    0.8,    0.6,    0.4,    0.0,    0.6,    0.0,    0.0,    0.0,    0.4,    0.4,    0.0,
 SAI_AUG = 0.6,    0.5,    1.2,    1.2,    0.8,    0.9,    0.6,    1.2,    1.2,    1.3,    0.9,    0.5,    0.0,    0.9,    0.0,    0.0,    0.0,    0.6,    0.6,    0.0,
 SAI_SEP = 0.6,    0.5,    1.0,    1.6,    1.0,    1.2,    0.8,    1.4,    1.3,    1.1,    0.9,    0.4,    0.0,    0.7,    0.0,    0.0,    0.0,    0.8,    0.7,    0.0,
 SAI_OCT = 0.7,    0.5,    0.8,    1.4,    1.0,    0.9,    0.7,    1.1,    0.7,    0.4,    0.6,    0.3,    0.0,    0.3,    0.0,    0.0,    0.0,    0.7,    0.5,    0.0,
 SAI_NOV = 0.6,    0.5,    0.6,    0.6,    0.5,    0.4,    0.3,    0.5,    0.4,    0.4,    0.4,    0.3,    0.0,    0.3,    0.0,    0.0,    0.0,    0.3,    0.3,    0.0,
 SAI_DEC = 0.5,    0.5,    0.5,    0.4,    0.4,    0.3,    0.2,    0.4,    0.4,    0.4,    0.3,    0.3,    0.0,    0.3,    0.0,    0.0,    0.0,    0.2,    0.2,    0.0,

 LAI_JAN = 4.0,    4.5,    0.0,    0.0,    2.0,    0.0,    0.0,    0.2,    0.3,    0.4,    0.2,    0.0,    0.0,    0.2,    0.0,    0.0,    0.0,    1.0,    0.6,    0.0,
 LAI_FEB = 4.0,    4.5,    0.0,    0.0,    2.0,    0.0,    0.0,    0.2,    0.3,    0.5,    0.3,    0.0,    0.0,    0.3,    0.0,    0.0,    0.0,    1.0,    0.6,    0.0,
 LAI_MAR = 4.0,    4.5,    0.0,    0.3,    2.2,    0.3,    0.2,    0.4,    0.5,    0.6,    0.3,    0.0,    0.0,    0.3,    0.0,    0.0,    0.0,    1.1,    0.7,    0.0,
 LAI_APR = 4.0,    4.5,    0.6,    1.2,    2.6,    0.9,    0.6,    1.0,    0.8,    0.7,    0.5,    0.0,    0.0,    0.4,    0.0,    0.0,    0.0,    1.3,    0.8,    0.0,
 LAI_MAY = 4.0,    4.5,    1.2,    3.0,    3.5,    2.2,    1.5,    2.4,    1.8,    1.2,    1.5,    1.0,    0.0,    1.1,    0.0,    0.0,    0.0,    1.7,    1.2,    0.0,
 LAI_JUN = 4.0,    4.5,    2.0,    4.7,    4.3,    3.5,    2.3,    4.1,    3.6,    3.0,    2.9,    2.0,    0.0,    2.5,    0.0,    0.0,    0.0,    2.1,    1.8,    0.0,
 LAI_JUL = 4.0,    4.5,    2.6,    4.5,    4.3,    3.5,    2.3,    4.1,    3.8,    3.5,    3.5,    3.0,    0.0,    3.2,    0.0,    0.0,    0.0,    2.1,    1.8,    0.0,
 LAI_AUG = 4.0,    4.5,    1.7,    3.4,    3.7,    2.5,    1.7,    2.7,    2.1,    1.5,    2.7,    3.0,    0.0,    2.2,    0.0,    0.0,    0.0,    1.8,    1.3,    0.0,
 LAI_SEP = 4.0,    4.5,    1.0,    1.2,    2.6,    0.9,    0.6,    1.0,    0.9,    0.7,    1.2,    1.5,    0.0,    1.1,    0.0,    0.0,    0.0,    1.3,    0.8,    0.0,
 LAI_OCT = 4.0,    4.5,    0.5,    0.3,    2.2,    0.3,    0.2,    0.4,    0.5,    0.6,    0.3,    0.0,    0.0,    0.3,    0.0,    0.0,    0.0,    1.1,    0.7,    0.0,
 LAI_NOV = 4.0,    4.5,    0.2,    0.0,    2.0,    0.0,    0.0,    0.2,    0.3,    0.5,    0.3,    0.0,    0.0,    0.3,    0.0,    0.0,    0.0,    1.0,    0.6,    0.0,
 LAI_DEC = 4.0,    4.5,    0.0,    0.0,    2.0,    0.0,    0.0,    0.2,    0.3,    0.4,    0.2,    0.0,    0.0,    0.2,    0.0,    0.0,    0.0,    1.0,    0.6,    0.0,

 SLAREA=0.0090, 0.0200, 0.0200, 0.0258, 0.0223, 0.0227, 0.0188, 0.0227, 0.0236, 0.0060, 0.0295, 0.0200, 0.0228, 0.0223,   0.02,   0.02, 0.0422,   0.02,   0.02,   0.02,

! Five types, one row for each type (BVOC currently not active).
 EPS1  =  0.46,   0.00,   0.00,  46.86,  30.98,  21.62,   0.11,  21.62,  22.80,   0.02,  0.815,   0.00,  41.87,   0.04,    0.0,    0.0,   2.31,    0.0,    0.0,    0.0,
 EPS2  =  3.34,   0.00,   0.00,   0.38,   0.96,   0.92,   0.22,   0.92,   0.59,   0.05,  0.535,   0.00,   0.98,   0.09,    0.0,    0.0,   1.47,    0.0,    0.0,    0.0,
 EPS3  =  1.85,   0.00,   0.00,   1.84,   1.84,   1.73,   1.26,   1.73,   1.37,   0.03,  0.605,   0.00,   1.82,   0.05,    0.0,    0.0,   1.70,    0.0,    0.0,    0.0,
 EPS4  =   0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,
 EPS5  =   0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,    0.0,

/

&noahmp_rad_parameters
 !------------------------------------------------------------------------------
 !                1       2       3       4       5       6       7       8     soil color index for soil albedo
 !------------------------------------------------------------------------------
 ALBSAT_VIS =   0.15,   0.11,   0.10,   0.09,   0.08,   0.07,   0.06,   0.05   ! saturated soil albedos
 ALBSAT_NIR =   0.30,   0.22,   0.20,   0.18,   0.16,   0.14,   0.12,   0.10   ! saturated soil albedos
 ALBDRY_VIS =   0.27,   0.22,   0.20,   0.18,   0.16,   0.14,   0.12,   0.10   ! dry soil albedos
 ALBDRY_NIR =   0.54,   0.44,   0.40,   0.36,   0.32,   0.28,   0.24,   0.20   ! dry soil albedos
 ALBICE     =   0.80,   0.55                                                   ! albedo land ice: 1=vis, 2=nir
 ALBLAK     =   0.60,   0.40                                                   ! albedo frozen lakes: 1=vis, 2=nir
 OMEGAS     =   0.8 ,   0.4                                                    ! two-stream parameter omega for snow
 BETADS     =   0.5                                                            ! two-stream parameter betad for snow
 BETAIS     =   0.5                                                            ! two-stream parameter betaI for snow
 EG         =   0.97,   0.98                                                   ! emissivity soil surface 1-soil;2-lake

/

&noahmp_global_parameters

! atmospheric constituants

  CO2    = 395.e-06     !co2 partial pressure
  O2     = 0.209        !o2 partial pressure

! runoff parameters used for SIMTOP and SIMGM:

  TIMEAN = 10.5         !gridcell mean topgraphic index (global mean)
  FSATMX = 0.38         !maximum surface saturated fraction (global mean)

! adjustable parameters for snow processes

  Z0SNO  = 0.002        !snow surface roughness length (m) (0.002)
  SSI    = 0.03         !liquid water holding capacity for snowpack (m3/m3) (0.03)
  SNOW_RET_FAC  = 5.e-5 !snowpack water release timescale factor (1/s)
  SWEMX  = 1.00         !new snow mass to fully cover old snow (mm)
                        !equivalent to 10mm depth (density = 100 kg/m3)
  TAU0          = 1.e6  !tau0 from Yang97 eqn. 10a
  GRAIN_GROWTH  = 5000. !growth from vapor diffusion Yang97 eqn. 10b
  EXTRA_GROWTH  = 10.   !extra growth near freezing Yang97 eqn. 10c
  DIRT_SOOT     = 0.3   !dirt and soot term Yang97 eqn. 10d
  BATS_COSZ     = 2.0   !zenith angle snow albedo adjustment; b in Yang97 eqn. 15
  BATS_VIS_NEW  = 0.95  !new snow visible albedo
  BATS_NIR_NEW  = 0.65  !new snow NIR albedo
  BATS_VIS_AGE  = 0.2   !age factor for diffuse visible snow albedo Yang97 eqn. 17
  BATS_NIR_AGE  = 0.5   !age factor for diffuse NIR snow albedo Yang97 eqn. 18
  BATS_VIS_DIR  = 0.4   !cosz factor for direct visible snow albedo Yang97 eqn. 15
  BATS_NIR_DIR  = 0.4   !cosz factor for direct NIR snow albedo Yang97 eqn. 16
  RSURF_SNOW = 50.0     !surface resistence for snow [s/m]
  RSURF_EXP = 5.0       !exponent in the shape parameter for soil resistance option 1

/

&noahmp_crop_parameters

 ! NCROP = 5
 !  1: Corn
 !  2: Soybean
 !  3: Sorghum
 !  4: Rice
 !  5: Winter wheat

DEFAULT_CROP = 0                                      ! The default crop type(1-5); if zero, use generic dynamic vegetation 

!----------------------------------------------------------
!                1       2       3       4       5
!----------------------------------------------------------
 
PLTDAY     =    130,    111,    111,    111,    111,  ! Planting date
HSDAY      =    280,    300,    300,    300,    300,  ! Harvest date
PLANTPOP   =   78.0,   78.0,   78.0,   78.0,   78.0,  ! Plant density [per ha] - used?
IRRI       =    0.0,    0.0,    0.0,    0.0,    0.0,  ! Irrigation strategy 0= non-irrigation 1=irrigation (no water-stress)

GDDTBASE   =   10.0,   10.0,   10.0,   10.0,   10.0,  ! Base temperature for GDD accumulation [C]
GDDTCUT    =   30.0,   30.0,   30.0,   30.0,   30.0,  ! Upper temperature for GDD accumulation [C]
GDDS1      =   60.0,   50.0,   50.0,   50.0,   50.0,  ! GDD from seeding to emergence
GDDS2      =  675.0,  718.0,  718.0,  718.0,  718.0,  ! GDD from seeding to initial vegetative 
GDDS3      = 1183.0,  933.0,  933.0,  933.0,  933.0,  ! GDD from seeding to post vegetative 
GDDS4      = 1253.0, 1103.0, 1103.0, 1103.0, 1103.0,  ! GDD from seeding to intial reproductive
GDDS5      = 1605.0, 1555.0, 1555.0, 1555.0, 1555.0,  ! GDD from seeding to pysical maturity 

C3C4       =      2,      1,      2,      2,      2,  ! photosynthetic pathway:  1. = c3 2. = c4
Aref       =    7.0,    7.0,    7.0,    7.0,    7.0,  ! reference maximum CO2 assimulation rate 
PSNRF      =   0.85,   0.85,   0.85,   0.85,   0.85,  ! CO2 assimulation reduction factor(0-1) (caused by non-modeling part,e.g.pest,weeds)
I2PAR      =    0.5,    0.5,    0.5,    0.5,    0.5,  ! Fraction of incoming solar radiation to photosynthetically active radiation
TASSIM0    =    8.0,    8.0,    8.0,    8.0,    8.0,  ! Minimum temperature for CO2 assimulation [C]
TASSIM1    =   18.0,   18.0,   18.0,   18.0,   18.0,  ! CO2 assimulation linearly increasing until temperature reaches T1 [C]
TASSIM2    =   30.0,   30.0,   30.0,   30.0,   30.0,  ! CO2 assmilation rate remain at Aref until temperature reaches T2 [C]
K          =   0.55,   0.55,   0.55,   0.55,   0.55,  ! light extinction coefficient
EPSI       =   12.5,   12.5,   12.5,   12.5,   12.5,  ! initial light use efficiency

Q10MR      =    2.0,    2.0,    2.0,    2.0,    2.0,  ! q10 for maintainance respiration
FOLN_MX    =    1.5,    1.5,    1.5,    1.5,    1.5,  ! foliage nitrogen concentration when f(n)=1 (%)
LEFREEZ    =    268,    268,    268,    268,    268,  ! characteristic T for leaf freezing [K]

DILE_FC_S1 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! coeficient for temperature leaf stress death [1/s]
DILE_FC_S2 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! One row for each of 8 stages
DILE_FC_S3 =    0.0,    0.0,    0.0,    0.0,    0.0,
DILE_FC_S4 =    0.0,    0.0,    0.0,    0.0,    0.0, 
DILE_FC_S5 =    0.5,    0.5,    0.5,    0.5,    0.5,
DILE_FC_S6 =    0.5,    0.5,    0.5,    0.5,    0.5,
DILE_FC_S7 =    0.0,    0.0,    0.0,    0.0,    0.0,
DILE_FC_S8 =    0.0,    0.0,    0.0,    0.0,    0.0,

DILE_FW_S1 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! coeficient for water leaf stress death [1/s]
DILE_FW_S2 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! One row for each of 8 stages
DILE_FW_S3 =    0.0,    0.0,    0.0,    0.0,    0.0,
DILE_FW_S4 =    0.0,    0.0,    0.0,    0.0,    0.0,
DILE_FW_S5 =    0.2,    0.2,    0.2,    0.2,    0.2,
DILE_FW_S6 =    0.2,    0.2,    0.2,    0.2,    0.2,
DILE_FW_S7 =    0.0,    0.0,    0.0,    0.0,    0.0,
DILE_FW_S8 =    0.0,    0.0,    0.0,    0.0,    0.0,

FRA_GR     =    0.2,    0.2,    0.2,    0.2,    0.2,  ! fraction of growth respiration

LF_OVRC_S1 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! fraction of leaf turnover  [1/s]
LF_OVRC_S2 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! One row for each of 8 stages
LF_OVRC_S3 =    0.0,    0.0,    0.0,    0.0,    0.0,
LF_OVRC_S4 =    0.0,    0.0,    0.0,    0.0,    0.0,
LF_OVRC_S5 =    0.2,   0.48,   0.48,   0.48,   0.48,
LF_OVRC_S6 =    0.3,   0.48,   0.48,   0.48,   0.48,
LF_OVRC_S7 =    0.0,    0.0,    0.0,    0.0,    0.0,
LF_OVRC_S8 =    0.0,    0.0,    0.0,    0.0,    0.0,

ST_OVRC_S1 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! fraction of stem turnover  [1/s]
ST_OVRC_S2 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! One row for each of 8 stages
ST_OVRC_S3 =    0.0,    0.0,    0.0,    0.0,    0.0,
ST_OVRC_S4 =    0.0,    0.0,    0.0,    0.0,    0.0,
ST_OVRC_S5 =   0.12,   0.12,   0.12,   0.12,   0.12,
ST_OVRC_S6 =   0.06,   0.06,   0.06,   0.06,   0.06,
ST_OVRC_S7 =    0.0,    0.0,    0.0,    0.0,    0.0,
ST_OVRC_S8 =    0.0,    0.0,    0.0,    0.0,    0.0,

RT_OVRC_S1 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! fraction of root tunrover  [1/s]
RT_OVRC_S2 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! One row for each of 8 stages
RT_OVRC_S3 =    0.0,    0.0,    0.0,    0.0,    0.0,
RT_OVRC_S4 =    0.0,    0.0,    0.0,    0.0,    0.0,
RT_OVRC_S5 =   0.12,   0.12,   0.12,   0.12,   0.12,
RT_OVRC_S6 =   0.06,   0.06,   0.06,   0.06,   0.06,
RT_OVRC_S7 =    0.0,    0.0,    0.0,    0.0,    0.0,
RT_OVRC_S8 =    0.0,    0.0,    0.0,    0.0,    0.0,

             
LFMR25     =    1.0,    1.0,    1.0,    1.0,    1.0,  !  leaf maintenance respiration at 25C [umol CO2/m**2  /s]
STMR25     =   0.05,    0.1,    0.1,    0.1,    0.1,  !  stem maintenance respiration at 25C [umol CO2/kg bio/s]
RTMR25     =   0.05,    0.0,    0.0,    0.0,    0.0,  !  root maintenance respiration at 25C [umol CO2/kg bio/s]
GRAINMR25  =    0.0,    0.1,    0.1,    0.1,    0.1,  ! grain maintenance respiration at 25C [umol CO2/kg bio/s]

LFPT_S1    =    0.0,    0.0,    0.0,    0.0,    0.0,  ! fraction of carbohydrate flux to leaf
LFPT_S2    =    0.0,    0.0,    0.0,    0.0,    0.0,  ! One row for each of 8 stages
LFPT_S3    =    0.4,    0.4,    0.4,    0.4,    0.4,
LFPT_S4    =    0.2,    0.2,    0.2,    0.2,    0.2,
LFPT_S5    =    0.0,    0.0,    0.0,    0.0,    0.0,
LFPT_S6    =    0.0,    0.0,    0.0,    0.0,    0.0,
LFPT_S7    =    0.0,    0.0,    0.0,    0.0,    0.0,
LFPT_S8    =    0.0,    0.0,    0.0,    0.0,    0.0,

STPT_S1    =    0.0,    0.0,    0.0,    0.0,    0.0,  ! fraction of carbohydrate flux to stem
STPT_S2    =    0.0,    0.0,    0.0,    0.0,    0.0,  ! One row for each of 8 stages
STPT_S3    =    0.2,    0.2,    0.2,    0.2,    0.2,
STPT_S4    =    0.5,    0.5,    0.5,    0.5,    0.5,
STPT_S5    =    0.0,   0.15,   0.15,   0.15,   0.15,
STPT_S6    =    0.0,   0.05,   0.05,   0.05,   0.05,
STPT_S7    =    0.0,    0.0,    0.0,    0.0,    0.0,
STPT_S8    =    0.0,    0.0,    0.0,    0.0,    0.0, 

RTPT_S1    =    0.0,    0.0,    0.0,    0.0,    0.0,  ! fraction of carbohydrate flux to root
RTPT_S2    =    0.0,    0.0,    0.0,    0.0,    0.0,  ! One row for each of 8 stages
RTPT_S3    =   0.34,    0.4,    0.4,    0.4,    0.4,
RTPT_S4    =    0.3,    0.3,    0.3,    0.3,    0.3,
RTPT_S5    =   0.05,   0.05,   0.05,   0.05,   0.05,
RTPT_S6    =    0.0,   0.05,   0.05,   0.05,   0.05,
RTPT_S7    =    0.0,    0.0,    0.0,    0.0,    0.0,
RTPT_S8    =    0.0,    0.0,    0.0,    0.0,    0.0,
   
GRAINPT_S1 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! fraction of carbohydrate flux to grain
GRAINPT_S2 =    0.0,    0.0,    0.0,    0.0,    0.0,  ! One row for each of 8 stages
GRAINPT_S3 =    0.0,    0.0,    0.0,    0.0,    0.0,
GRAINPT_S4 =    0.0,    0.0,    0.0,    0.0,    0.0,
GRAINPT_S5 =   0.95,    0.8,    0.8,    0.8,    0.8,
GRAINPT_S6 =    1.0,    0.9,    0.9,    0.9,    0.9,
GRAINPT_S7 =    0.0,    0.0,    0.0,    0.0,    0.0,
GRAINPT_S8 =    0.0,    0.0,    0.0,    0.0,    0.0,


BIO2LAI   =  0.035,  0.015,  0.015,  0.015,  0.015,  ! leaf are per living leaf biomass [m^2/kg]

/

&noahmp_optional_parameters

 !------------------------------------------------------------------------------
 ! Saxton and Rawls 2006 Pedo-transfer function coefficients
 !------------------------------------------------------------------------------

 sr2006_theta_1500t_a =   -0.024   ! sand coefficient
 sr2006_theta_1500t_b =    0.487   ! clay coefficient
 sr2006_theta_1500t_c =    0.006   ! orgm coefficient
 sr2006_theta_1500t_d =    0.005   ! sand*orgm coefficient
 sr2006_theta_1500t_e =   -0.013   ! clay*orgm coefficient
 sr2006_theta_1500t_f =    0.068   ! sand*clay coefficient
 sr2006_theta_1500t_g =    0.031   ! constant adjustment

 sr2006_theta_1500_a  =    0.14    ! theta_1500t coefficient
 sr2006_theta_1500_b  =   -0.02    ! constant adjustment

 sr2006_theta_33t_a   =   -0.251   ! sand coefficient
 sr2006_theta_33t_b   =    0.195   ! clay coefficient
 sr2006_theta_33t_c   =    0.011   ! orgm coefficient
 sr2006_theta_33t_d   =    0.006   ! sand*orgm coefficient
 sr2006_theta_33t_e   =   -0.027   ! clay*orgm coefficient
 sr2006_theta_33t_f   =    0.452   ! sand*clay coefficient
 sr2006_theta_33t_g   =    0.299   ! constant adjustment

 sr2006_theta_33_a    =    1.283   ! theta_33t*theta_33t coefficient
 sr2006_theta_33_b    =   -0.374   ! theta_33t coefficient
 sr2006_theta_33_c    =   -0.015   ! constant adjustment

 sr2006_theta_s33t_a  =    0.278   ! sand coefficient
 sr2006_theta_s33t_b  =    0.034   ! clay coefficient
 sr2006_theta_s33t_c  =    0.022   ! orgm coefficient
 sr2006_theta_s33t_d  =   -0.018   ! sand*orgm coefficient
 sr2006_theta_s33t_e  =   -0.027   ! clay*orgm coefficient
 sr2006_theta_s33t_f  =   -0.584   ! sand*clay coefficient
 sr2006_theta_s33t_g  =    0.078   ! constant adjustment

 sr2006_theta_s33_a   =    0.636   ! theta_s33t coefficient
 sr2006_theta_s33_b   =   -0.107   ! constant adjustment

 sr2006_psi_et_a      =  -21.67    ! sand coefficient
 sr2006_psi_et_b      =  -27.93    ! clay coefficient
 sr2006_psi_et_c      =  -81.97    ! theta_s33 coefficient
 sr2006_psi_et_d      =   71.12    ! sand*theta_s33 coefficient
 sr2006_psi_et_e      =    8.29    ! clay*theta_s33 coefficient
 sr2006_psi_et_f      =   14.05    ! sand*clay coefficient
 sr2006_psi_et_g      =   27.16    ! constant adjustment

 sr2006_psi_e_a       =    0.02    ! psi_et*psi_et coefficient
 sr2006_psi_e_b       =   -0.113   ! psi_et coefficient
 sr2006_psi_e_c       =   -0.7     ! constant adjustment

 sr2006_smcmax_a      =   -0.097   ! sand adjustment
 sr2006_smcmax_b      =    0.043   ! constant adjustment
 
/
