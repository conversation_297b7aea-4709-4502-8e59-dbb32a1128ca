.IGNORE:

ifeq ($(SPATIAL_SOIL),1)
SPATIAL_SOIL = -DSPATIAL_SOIL
else
SPATIAL_SOIL = 
endif

ifeq ($(HYDRO_REALTIME),1)
HYDRO_REALTIME = -DHY<PERSON>O_REALTIME
else
HYDRO_REALTIME =
endif

ifeq ($(WRF_HYDRO),1)
WRF_HYDRO = -DWRF_HYDRO $(HYDRO_REALTIME)
else
WRF_HYDRO =
endif

ifeq ($(WRF_HYDRO_RAPID),1)
WRF_HYDRO = -DWRF_HYDRO -DWRF_HYDRO_RAPID $(HYDRO_REALTIME)
endif

ifeq ($(HYDRO_D),1)
HYDRO_D = -DHYDRO_D $(WRF_HYDRO)
else
HYDRO_D =  $(WRF_HYDRO)
endif


ifeq ($(WRF_HYDRO_NUDGING),1)
WR<PERSON>_HYDRO_NUDGING = -DWRF_HYDRO_NUDGING
else
WRF_HYDRO_NUDGING = 
endif

ifeq ($(OUTPUT_CHAN_CONN),1)
OUTPUT_CHAN_CONN = -DOUTPUT_CHAN_CONN
else
OUTPUT_CHAN_CONN = 
endif


RMD		=	ls 
RM		=	rm -f
COMPILER90=	pgf90 
F90FLAGS  =     -Mfree -Mfptrap -c -byteswapio -Ktrap=fp -O2 -Kieee
LDFLAGS  =      $(F90FLAGS)
MODFLAG	=	-I./ -I ../mod
LDFLAGS	=	
CPPINVOKE	=
CPPFLAGS	=       -I ../Data_Rec $(HYDRO_D) $(WRF_HYDRO) $(SPATIAL_SOIL) $(WRF_HYDRO_NUDGING) $(OUTPUT_CHAN_CONN)
LIBS 	=	
NETCDFINC       =       $(NETCDF_INC)
NETCDFLIB       =       -L$(NETCDF_LIB) -lnetcdff -lnetcdf
