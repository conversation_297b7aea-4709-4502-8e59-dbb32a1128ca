!*******************************************************************************
!Subroutine - rapid_open_Qhum
!*******************************************************************************
subroutine rapid_open_Qhum_file(Qhum_file) 

!Purpose:
!Open Qhum_file from Fortran.
!Author: 
!Cedric H. David, 2014-2015.


!*******************************************************************************
!Global variables
!*******************************************************************************
use rapid_var, only :                                                          &
                   rank

implicit none


!*******************************************************************************
!Includes
!*******************************************************************************


!*******************************************************************************
!Intent (in/out), and local variables 
!*******************************************************************************
character(len=100), intent(in):: Qhum_file


!*******************************************************************************
!Open file
!*******************************************************************************
if (rank==0) open(36,file=Qhum_file,status='old')


!*******************************************************************************
!End 
!*******************************************************************************

end subroutine rapid_open_Qhum_file

