!*******************************************************************************
!Subroutine - rapid_close_Qobs_file 
!*******************************************************************************
subroutine rapid_close_Qobs_file

!Purpose:
!Close Qobs_file from Fortran.
!Author: 
!Cedric H. David, 2013-2015.


!*******************************************************************************
!Global variables
!*******************************************************************************
use rapid_var, only :                                                          &
                   rank


implicit none


!*******************************************************************************
!Intent (in/out), and local variables 
!*******************************************************************************


!*******************************************************************************
!Includes
!*******************************************************************************


!*******************************************************************************
!Close file
!*******************************************************************************
if (rank==0) close(33)

!*******************************************************************************
!End subroutine
!*******************************************************************************
end subroutine rapid_close_Qobs_file
