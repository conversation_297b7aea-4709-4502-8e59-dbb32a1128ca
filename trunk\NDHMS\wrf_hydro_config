#!/usr/bin/perl
#input argument: Compiler/System sequential/parallel
#This is called by WRF configuration only.
if($#ARGV ne 1) {
  print("Error: No such configuration for Hydro \n");
  exit(1);
}
         $x = lc(shift(@ARGV));
         $paropt = lc(shift(@ARGV));

         print("Configure option for Hydro : $x  $paropt \n");  
          if($x =~ "pgi") {
              if($paropt eq 'serial') { 
                 # system("./configure 1");
                 print "Error : option not defined in WRF-Hyro. \n";
                 exit(1);
              }
              else {system("./configure 1"); exit(0);}
          }
          if($x =~ "aix") {
              print "Error : option not defined. \n";
              exit(1);
              if($paropt eq 'serial') { system("./configure 3");}
              else {system("./configure 4");}
          }
          if($x =~ "gfortran") {
              if($paropt eq 'serial') { 
                 # system("./configure 5");
                 print "Error : option not defined in WRF-Hyro. \n";
                 exit(1);
              }
              else {system("./configure 2"); exit(0);}
          }
          if($x =~ "ifort") {
              if($paropt eq 'serial') { 
                   #system("./configure 7");
                 print "Error : option not defined. \n";
                 exit(1);
              }
              else {system("./configure 3"); exit(0);}
          }
                 print "Error : option not defined. \n";
                 exit(1);

