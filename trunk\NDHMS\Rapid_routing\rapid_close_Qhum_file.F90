!*******************************************************************************
!Subroutine - rapid_close_Qhum_file 
!*******************************************************************************
subroutine rapid_close_Qhum_file

!Purpose:
!Close Qhum_file from Fortran.
!Author: 
!Cedric H. David, 2014-2015.


!*******************************************************************************
!Global variables
!*******************************************************************************
use rapid_var, only :                                                          &
                   rank


implicit none


!*******************************************************************************
!Intent (in/out), and local variables 
!*******************************************************************************


!*******************************************************************************
!Includes
!*******************************************************************************


!*******************************************************************************
!Close file
!*******************************************************************************
if (rank==0) close(36)

!*******************************************************************************
!End subroutine
!*******************************************************************************
end subroutine rapid_close_Qhum_file
