&hydro_nlist
    aggfactrt = 4
    channel_option = 2
    chanobs_domain = 1
    chanrtswcrt = 1
    chrtout_domain = 1
    chrtout_grid = 1
    compound_channel = .true.
    dtrt_ch = 300
    dtrt_ter = 10
    dxrt = 250
    frxst_pts_out = 0
    geo_finegrid_flnm = './NWM/DOMAIN/Fulldom_hires.nc'
    geo_static_flnm = './NWM/DOMAIN/geo_em.d01.nc'
    gw_restart = 1
    gwbaseswcrt = 4
    gwbuckparm_file = './NWM/DOMAIN/GWBUCKPARM.nc'
    hydrotbl_f = './NWM/DOMAIN/hydro2dtbl.nc'
    igrid = 1
    io_config_outputs = 0
    io_form_outputs = 4
    land_spatial_meta_flnm = './NWM/DOMAIN/GEOGRID_LDASOUT_Spatial_Metadata.nc'
    lsmout_domain = 1
    nsoil = 4
    order_to_write = 1
    out_dt = 60
    outlake = 1
    output_channelbucket_influx = 0
    output_gw = 1
    ovrtswcrt = 1
    reservoir_persistence_usgs = .false.
    restart_file = './NWM/RESTART/HYDRO_RST.2011-08-26_00:00_DOMAIN1'
    route_lake_f = './NWM/DOMAIN/LAKEPARM.nc'
    route_link_f = './NWM/DOMAIN/Route_Link.nc'
    rst_bi_in = 0
    rst_bi_out = 0
    rst_dt = 1440
    rst_typ = 1
    rstrt_swc = 0
    rt_option = 1
    rtout_domain = 1
    split_output_count = 1
    subrtswcrt = 1
    sys_cpl = 1
    t0outputflag = 1
    udmap_file = './NWM/DOMAIN/spatialweights.nc'
    udmp_opt = 1
    zsoil8 = -0.1, -0.4, -1.0, -2.0
/

&nudging_nlist
    biaswindowbeforet0 = .false.
    invdisttimeweightbias = .true.
    maxagepairsbiaspersist = 3
    minnumpairsbiaspersist = 1
    nlastobs = 480
    noconstinterfbias = .false.
    nudginglastobsfile = './NWM/RESTART/nudgingLastObs.2011-08-26_00:00:00.nc'
    nudgingparamfile = './NWM/DOMAIN/nudgingParams.nc'
    persistbias = .true.
    readtimesliceparallel = .true.
    temporalpersistence = .true.
    timeslicepath = './NWM/nudgingTimeSliceObs/'
/
