!*******************************************************************************
!Subroutine - rapid_close_Qfor_file 
!*******************************************************************************
subroutine rapid_close_Qfor_file

!Purpose:
!Close Qfor_file from Fortran.
!Author: 
!Cedric H. David, 2013-2015.


!*******************************************************************************
!Global variables
!*******************************************************************************
use rapid_var, only :                                                          &
                   rank


implicit none


!*******************************************************************************
!Intent (in/out), and local variables 
!*******************************************************************************


!*******************************************************************************
!Includes
!*******************************************************************************


!*******************************************************************************
!Close file
!*******************************************************************************
if (rank==0) close(34)

!*******************************************************************************
!End subroutine
!*******************************************************************************
end subroutine rapid_close_Qfor_file
