#!/bin/bash

theArgument=$1

###################################
## Setup the HOSTNAME if not set

if [ -z ${HOSTNAME+x} ]; then
  HOSTNAME=`hostname`
  echo "Configured: hostname=$HOSTNAME"
fi

###################################
## Setup the NetCDF include and LIB variables.
## If Neither is set and neither $NETCDF nor $NETCDF_DIR is not set,
## then try nc-config. If that fails, all fails.

if [[ -z $NETCDF ]]; then
    if [[ -n $NETCDF_DIR ]]; then
	NETCDF=$NETCDF_DIR
    fi
fi

if [[ -z $NETCDF_INC ]]; then
    if [[ -z $NETCDF ]]; then
	NETCDF_INC=`nc-config --includedir 2> /dev/null`
    else
	NETCDF_INC=${NETCDF}/include
    fi
    if [[ -z $NETCDF_INC ]]; then
	echo "Error: environment variable NETCDF_INC not defined."
	exit 1
    fi
    echo "NETCDF_INC = ${NETCDF_INC}" > macros.tmp
fi

if [[ -z $NETCDF_LIB ]]; then
    if [[ -z $NETCDF ]]; then
	NETCDF_LIB=`nc-config --libs | cut -c3- | cut -d' ' -f1`
    else
	NETCDF_LIB=${NETCDF}/lib
    fi
    if [[ -z $NETCDF_LIB ]]; then
	echo "Error: environment variable NETCDF_LIB not defined."
	exit 1
     fi
    echo "NETCDF_LIB = ${NETCDF_LIB}" >> macros.tmp
fi

if [[ ! -e ${NETCDF_LIB}/libnetcdff.a ]]; then
    echo "NETCDFLIB       =       -L${NETCDF_LIB} -lnetcdf" >> macros.tmp 
fi
 
###################################
## File/dir setups
if [[ -e macros ]]; then rm -f macros; fi
if [[ ! -e lib ]]; then mkdir lib; fi
if [[ ! -e mod ]]; then mkdir mod; fi


###################################
## If no argument was supplied, get all interactive.
if [[ -z $theArgument ]]; then
    echo "Please select from following supported linux compilers"
    echo "using either the number or key (not case sensitive):"
    echo 
    echo "Number           Key  Description"
    echo "---------------------------------------------------"
    echo "     1           pgi  PGI parallel"
    echo "     2         gfort  gfortran parallel"
    echo "     3         ifort  intel parallel (incl. Theia, Gordon, Summit)"
    echo "     4          luna  intel parallel (WCOSS Luna)"
    echo "     5     ifort_omp  intel openmp"
    echo "     6 intel.cray_xc  intel parallel (cray_xc)"
    echo "     7  cray_fortran  Cray Fortran PE (ftn)"
    echo "     0          exit  exit"
    echo 
    read -p "Enter selection: " theArgument
    echo
fi

## remove case sensitivity
theArgument=`echo $theArgument | tr '[:upper:]' '[:lower:]'`


###################################
## What to do with the choice

if [[ "$theArgument" == "1" ]] || [[ "$theArgument" == "pgi" ]]; then
    cp arc/macros.mpp.linux macros 
    cp arc/Makefile.mpp Makefile.comm 
    echo "Configured: PGI"
fi

if [[ "$theArgument" == "2" ]] || [[ "$theArgument" == "gfort" ]]; then
    cp arc/macros.mpp.gfort macros 
    cp arc/Makefile.mpp Makefile.comm
    echo "Configured: gfort"
fi

if [[ "$theArgument" == "3" ]] || [[ "$theArgument" == "ifort" ]]; then

    ## theia login machines self identify as "tfe" and have
    ## their own intel macros. We handle luna more explicitly... 
    if [[ $HOSTNAME = *tfe* ]]; then
        cp arc/macros.mpp.ifort.theia macros
        echo "Configured: ifort on Theia"

    elif [[ $HOSTNAME = *gordon* ]]; then
        cp arc/macros.mpp.intel.cray_xc macros
        echo "Configured: ifort on Gordon"

    elif [[ $HOSTNAME = *shas* ]]; then
        cp arc/macros.mpp.ifort.summit_has macros
        echo "Configured: ifort on Summit haswell"

    else
	cp arc/macros.mpp.ifort macros 
	echo "Configured: ifort"
    fi
    cp arc/Makefile.mpp Makefile.comm
fi

if [[ "$theArgument" == "4" ]] || [[ "$theArgument" == "luna" ]]; then
    cp arc/macros.mpp.ifort.luna macros 
    cp arc/Makefile.mpp Makefile.comm
    echo "Configured: ifort on Luna"
fi

if [[ "$theArgument" == "5" ]] || [[ "$theArgument" == "ifort_omp" ]]; then
    ## theia login machines self identify as "tfe" and have
    ## their own intel macros. We handle luna more explicitly... 
    if [[ $HOSTNAME != *tfe* ]]; then
	cp arc/macros.mpp.ifort.omp macros 
	echo "Configured: ifort with OpenMP"
    else
	cp arc/macros.mpp.ifort.theia macros
	echo "Configured: ifort on Theia"
    fi
    cp arc/Makefile.mpp Makefile.comm
fi

if [[ "$theArgument" == "6" ]] || [[ "$theArgument" == "intel.cray_xc" ]]; then
    cp arc/macros.mpp.intel.cray_xc macros
    cp arc/Makefile.mpp Makefile.comm
    echo "Configured: ifort on cray_xc"
fi

if [[ "$theArgument" == "7" ]] || [[ "$theArgument" == "cray_fortran" ]]; then
    cp arc/macros.mpp.cray_fortran macros
    cp arc/Makefile.mpp Makefile.comm
    echo "Configured: Cray Fortran PrgEnv"
fi


## The above result in a new macros file which was
## previously deleted. If it does not exist, none
## were chosen.
if [[ ! -e macros ]]; then
    echo "No compiler selected. Exiting"
    if [[ -e macros.tmp ]]; then rm -f macros.tmp; fi
    # failure
    exit 1
fi

# PGI sequential
# cp arc/macros.seq.linux macros
# cp arc/Makefile.seq Makefile.comm
# gfortran sequential                         
#zystem "cp arc/macros.seq.gfort macros 
#cp arc/Makefile.seq Makefile.comm 
# ifort sequential                            
#cp arc/macros.seq.ifort macros 
#cp arc/Makefile.seq Makefile.comm

if [[ -e macros.tmp ]]; then
    cat macros macros.tmp > macros.a
    rm -f macros.tmp
    mv macros.a macros
fi

## success
exit 0
