netcdf HYDRO_RST {
dimensions:
	depth = 4 ;
	ix = 15 ;
	iy = 16 ;
	ixrt = 60 ;
	iyrt = 64 ;
	links = 185 ;
	basns = 185 ;
	lakes = 1 ;
variables:
	float stc1(iy, ix) ;
	float smc1(iy, ix) ;
	float sh2ox1(iy, ix) ;
	float stc2(iy, ix) ;
	float smc2(iy, ix) ;
	float sh2ox2(iy, ix) ;
	float stc3(iy, ix) ;
	float smc3(iy, ix) ;
	float sh2ox3(iy, ix) ;
	float stc4(iy, ix) ;
	float smc4(iy, ix) ;
	float sh2ox4(iy, ix) ;
	float infxsrt(iy, ix) ;
	float soldrain(iy, ix) ;
	float sfcheadrt(iy, ix) ;
	float QBDRYRT(iyrt, ixrt) ;
	float infxswgt(iyrt, ixrt) ;
	float sfcheadsubrt(iyrt, ixrt) ;
	float sh2owgt1(iyrt, ixrt) ;
	float sh2owgt2(iyrt, ixrt) ;
	float sh2owgt3(iyrt, ixrt) ;
	float sh2owgt4(iyrt, ixrt) ;
	float qstrmvolrt(iyrt, ixrt) ;
	float hlink(links) ;
	float qlink1(links) ;
	float qlink2(links) ;
	float resht(lakes) ;
	float qlakeo(lakes) ;
	float qlakei(lakes) ;
	float lake_inflort(iyrt, ixrt) ;
	float z_gwsubbas(links) ;

// global attributes:
		:his_out_counts = 41 ;
		:Restart_Time = "2011-08-26_00:00:00" ;
		:Since_Date = "2007-01-01_00:00:00" ;
		:DTCT = 300.f ;
		:channel_only = 0 ;
		:channelBucket_only = 0 ;
}
