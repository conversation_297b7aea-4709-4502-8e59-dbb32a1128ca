.IGNORE:

ifeq ($(HYDRO_REALTIME),1)
HYDRO_REALTIME = -DHYDRO_REALTIME
else
HYDRO_REALTIME =
endif

ifeq ($(WRF_HYDRO),1)
WRF_HYDRO = -DWRF_HYDRO $(HYDRO_REALTIME)
else
WRF_HYDRO =
endif

ifeq ($(WRF_HYDRO_RAPID),1)
WRF_HYDRO = -DWRF_HYDRO -DWRF_HYDRO_RAPID $(HYDRO_REALTIME)
endif

ifeq ($(HYDRO_D),1)
HYDRO_D = -DHYDRO_D $(WRF_HYDRO)
else
HYDRO_D =  $(WRF_HYDRO)
endif


#################
# NEMS Settings #
#################

FC          = mpiifort -g -openmp -mkl=sequential -align array32byte -lmkl_intel_lp64 -lmkl_core -lmkl_sequential -lpthread -openmp -convert big_endian -assume byterecl -mkl=sequential

# -g	: produce symbolic debug information in object file

######################
# WRF Hydro Settings #
######################

RMD		= rm -f
COMPILER90	= $(FC)
F90FLAGS	= -O2 -g -w -c -ftz -fno-alias -fp-model precise -FR
# -w	: disable all warnings
# -c	: compile to object (.o) only, do not link
# -ftz	: enable flush denormal results to zero
MODFLAG		= -I./ -I../../MPP -I../MPP -I../mod
LDFLAGS		= 
CPPINVOKE	= -fpp
CPPFLAGS	= -DMPP_LAND -I ../Data_Rec $(HYDRO_D)
LIBS		=	
NETCDFINC       = $(NETCDF)/include
NETCDFLIB       = -L$(NETCDF)/lib -lnetcdff -lnetcdf
