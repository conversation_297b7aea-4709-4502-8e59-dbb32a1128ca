.IGNORE:

ifeq ($(SPATIAL_SOIL),1)
SPATIAL_SOIL = -DSPATIAL_SOIL
else
SPATIAL_SOIL = 
endif

ifeq ($(HYDRO_REALTIME),1)
HYDRO_REALTIME = -DHYDRO_REALTIME
else
HYDRO_REALTIME =
endif

ifeq ($(WRF_HYDRO),1)
WRF_HYDRO = -DWRF_HYDRO $(HYDRO_REALTIME)
else
WRF_HYDRO =
endif

ifeq ($(WRF_HYDRO_RAPID),1)
WRF_HYDRO = -DWRF_HYDRO -DWRF_HYDRO_RAPID $(HYDRO_REALTIME)
endif

ifeq ($(HYDRO_D),1)
HYDRO_D = -DHYDRO_D $(WRF_HYDRO)
else
HYDRO_D =  $(WRF_HYDRO)
endif



RMD		=	rm -f
COMPILER90=	gfortran
F90FLAGS  =       -w -c -ffree-form -ffree-line-length-none -fconvert=big-endian -frecord-marker=4 
MODFLAG	=	-I./ -I../mod
LDFLAGS	=	
CPPINVOKE 	=       -cpp
CPPFLAGS	=       -I"../Data_Rec" $(HYDRO_D) $(SPATIAL_SOIL)
LIBS 	=	
NETCDFINC       =       $(NETCDF_INC)
NETCDFLIB       =       -L$(NETCDF_LIB) -lnetcdff -lnetcdf
