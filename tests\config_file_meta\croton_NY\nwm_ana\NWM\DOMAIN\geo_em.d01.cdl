netcdf geo_em.d01 {
dimensions:
	Time = UNLIMITED ; // (1 currently)
	month = 12 ;
	south_north = 16 ;
	west_east = 15 ;
	land_cat = 24 ;
	west_east_stag = 16 ;
	south_north_stag = 17 ;
	soil_cat = 16 ;
	DateStrLen = 19 ;
variables:
	float ALBEDO12M(Time, month, south_north, west_east) ;
		ALBEDO12M:FieldType = 104 ;
		ALBEDO12M:MemoryOrder = "XYZ" ;
		ALBEDO12M:units = "percent" ;
		ALBEDO12M:description = "Monthly surface albedo" ;
		ALBEDO12M:stagger = "M" ;
		ALBEDO12M:sr_x = 1 ;
		ALBEDO12M:sr_y = 1 ;
	float CLAT(Time, south_north, west_east) ;
		CLAT:FieldType = 104 ;
		CLAT:MemoryOrder = "XY " ;
		CLAT:units = "degrees latitude" ;
		CLAT:description = "Computational latitude on mass grid" ;
		CLAT:stagger = "M" ;
		CLAT:sr_x = 1 ;
		CLAT:sr_y = 1 ;
	float CLONG(Time, south_north, west_east) ;
		CLONG:FieldType = 104 ;
		CLONG:MemoryOrder = "XY " ;
		CLONG:units = "degrees longitude" ;
		CLONG:description = "Computational longitude on mass grid" ;
		CLONG:stagger = "M" ;
		CLONG:sr_x = 1 ;
		CLONG:sr_y = 1 ;
	float CON(Time, south_north, west_east) ;
		CON:FieldType = 104 ;
		CON:MemoryOrder = "XY " ;
		CON:units = "whoknows" ;
		CON:description = "something" ;
		CON:stagger = "M" ;
		CON:sr_x = 1 ;
		CON:sr_y = 1 ;
	float COSALPHA(Time, south_north, west_east) ;
		COSALPHA:FieldType = 104 ;
		COSALPHA:MemoryOrder = "XY " ;
		COSALPHA:units = "none" ;
		COSALPHA:description = "Cosine of rotation angle" ;
		COSALPHA:stagger = "M" ;
		COSALPHA:sr_x = 1 ;
		COSALPHA:sr_y = 1 ;
	float E(Time, south_north, west_east) ;
		E:FieldType = 104 ;
		E:MemoryOrder = "XY " ;
		E:units = "-" ;
		E:description = "Coriolis E parameter" ;
		E:stagger = "M" ;
		E:sr_x = 1 ;
		E:sr_y = 1 ;
	float F(Time, south_north, west_east) ;
		F:FieldType = 104 ;
		F:MemoryOrder = "XY " ;
		F:units = "-" ;
		F:description = "Coriolis F parameter" ;
		F:stagger = "M" ;
		F:sr_x = 1 ;
		F:sr_y = 1 ;
	float GREENFRAC(Time, month, south_north, west_east) ;
		GREENFRAC:FieldType = 104 ;
		GREENFRAC:MemoryOrder = "XYZ" ;
		GREENFRAC:units = "fraction" ;
		GREENFRAC:description = "Monthly green fraction" ;
		GREENFRAC:stagger = "M" ;
		GREENFRAC:sr_x = 1 ;
		GREENFRAC:sr_y = 1 ;
	float HGT_M(Time, south_north, west_east) ;
		HGT_M:FieldType = 104 ;
		HGT_M:MemoryOrder = "XY " ;
		HGT_M:units = "meters MSL" ;
		HGT_M:description = "Topography height" ;
		HGT_M:stagger = "M" ;
		HGT_M:sr_x = 1 ;
		HGT_M:sr_y = 1 ;
	float LAI12M(Time, month, south_north, west_east) ;
		LAI12M:FieldType = 104 ;
		LAI12M:MemoryOrder = "XYZ" ;
		LAI12M:units = "m^2/m^2" ;
		LAI12M:description = "MODIS LAI" ;
		LAI12M:stagger = "M" ;
		LAI12M:sr_x = 1 ;
		LAI12M:sr_y = 1 ;
	float LAKE_DEPTH(Time, south_north, west_east) ;
		LAKE_DEPTH:FieldType = 104 ;
		LAKE_DEPTH:MemoryOrder = "XY " ;
		LAKE_DEPTH:units = "meters MSL" ;
		LAKE_DEPTH:description = "Topography height" ;
		LAKE_DEPTH:stagger = "M" ;
		LAKE_DEPTH:sr_x = 1 ;
		LAKE_DEPTH:sr_y = 1 ;
	float LANDMASK(Time, south_north, west_east) ;
		LANDMASK:FieldType = 104 ;
		LANDMASK:MemoryOrder = "XY " ;
		LANDMASK:description = "Landmask : 1=land, 0=water" ;
		LANDMASK:sr_x = 1 ;
		LANDMASK:sr_y = 1 ;
		LANDMASK:stagger = "M" ;
		LANDMASK:units = "none" ;
	float LANDUSEF(Time, land_cat, south_north, west_east) ;
		LANDUSEF:FieldType = 104 ;
		LANDUSEF:MemoryOrder = "XYZ" ;
		LANDUSEF:units = "category" ;
		LANDUSEF:description = "24-category USGS landuse" ;
		LANDUSEF:stagger = "M" ;
		LANDUSEF:sr_x = 1 ;
		LANDUSEF:sr_y = 1 ;
	float LU_INDEX(Time, south_north, west_east) ;
		LU_INDEX:FieldType = 104 ;
		LU_INDEX:MemoryOrder = "XY " ;
		LU_INDEX:description = "Dominant category" ;
		LU_INDEX:sr_x = 1 ;
		LU_INDEX:sr_y = 1 ;
		LU_INDEX:stagger = "M" ;
		LU_INDEX:units = "category" ;
	float MAPFAC_M(Time, south_north, west_east) ;
		MAPFAC_M:FieldType = 104 ;
		MAPFAC_M:MemoryOrder = "XY " ;
		MAPFAC_M:units = "none" ;
		MAPFAC_M:description = "Mapfactor on mass grid" ;
		MAPFAC_M:stagger = "M" ;
		MAPFAC_M:sr_x = 1 ;
		MAPFAC_M:sr_y = 1 ;
	float MAPFAC_MX(Time, south_north, west_east) ;
		MAPFAC_MX:FieldType = 104 ;
		MAPFAC_MX:MemoryOrder = "XY " ;
		MAPFAC_MX:units = "none" ;
		MAPFAC_MX:description = "Mapfactor (x-dir) on mass grid" ;
		MAPFAC_MX:stagger = "M" ;
		MAPFAC_MX:sr_x = 1 ;
		MAPFAC_MX:sr_y = 1 ;
	float MAPFAC_MY(Time, south_north, west_east) ;
		MAPFAC_MY:FieldType = 104 ;
		MAPFAC_MY:MemoryOrder = "XY " ;
		MAPFAC_MY:units = "none" ;
		MAPFAC_MY:description = "Mapfactor (y-dir) on mass grid" ;
		MAPFAC_MY:stagger = "M" ;
		MAPFAC_MY:sr_x = 1 ;
		MAPFAC_MY:sr_y = 1 ;
	float MAPFAC_U(Time, south_north, west_east_stag) ;
		MAPFAC_U:FieldType = 104 ;
		MAPFAC_U:MemoryOrder = "XY " ;
		MAPFAC_U:units = "none" ;
		MAPFAC_U:description = "Mapfactor on U grid" ;
		MAPFAC_U:stagger = "U" ;
		MAPFAC_U:sr_x = 1 ;
		MAPFAC_U:sr_y = 1 ;
	float MAPFAC_UX(Time, south_north, west_east_stag) ;
		MAPFAC_UX:FieldType = 104 ;
		MAPFAC_UX:MemoryOrder = "XY " ;
		MAPFAC_UX:units = "none" ;
		MAPFAC_UX:description = "Mapfactor (x-dir) on U grid" ;
		MAPFAC_UX:stagger = "U" ;
		MAPFAC_UX:sr_x = 1 ;
		MAPFAC_UX:sr_y = 1 ;
	float MAPFAC_UY(Time, south_north, west_east_stag) ;
		MAPFAC_UY:FieldType = 104 ;
		MAPFAC_UY:MemoryOrder = "XY " ;
		MAPFAC_UY:units = "none" ;
		MAPFAC_UY:description = "Mapfactor (y-dir) on U grid" ;
		MAPFAC_UY:stagger = "U" ;
		MAPFAC_UY:sr_x = 1 ;
		MAPFAC_UY:sr_y = 1 ;
	float MAPFAC_V(Time, south_north_stag, west_east) ;
		MAPFAC_V:FieldType = 104 ;
		MAPFAC_V:MemoryOrder = "XY " ;
		MAPFAC_V:units = "none" ;
		MAPFAC_V:description = "Mapfactor on V grid" ;
		MAPFAC_V:stagger = "V" ;
		MAPFAC_V:sr_x = 1 ;
		MAPFAC_V:sr_y = 1 ;
	float MAPFAC_VX(Time, south_north_stag, west_east) ;
		MAPFAC_VX:FieldType = 104 ;
		MAPFAC_VX:MemoryOrder = "XY " ;
		MAPFAC_VX:units = "none" ;
		MAPFAC_VX:description = "Mapfactor (x-dir) on V grid" ;
		MAPFAC_VX:stagger = "V" ;
		MAPFAC_VX:sr_x = 1 ;
		MAPFAC_VX:sr_y = 1 ;
	float MAPFAC_VY(Time, south_north_stag, west_east) ;
		MAPFAC_VY:FieldType = 104 ;
		MAPFAC_VY:MemoryOrder = "XY " ;
		MAPFAC_VY:units = "none" ;
		MAPFAC_VY:description = "Mapfactor (y-dir) on V grid" ;
		MAPFAC_VY:stagger = "V" ;
		MAPFAC_VY:sr_x = 1 ;
		MAPFAC_VY:sr_y = 1 ;
	float OA1(Time, south_north, west_east) ;
		OA1:FieldType = 104 ;
		OA1:MemoryOrder = "XY " ;
		OA1:units = "whoknows" ;
		OA1:description = "something" ;
		OA1:stagger = "M" ;
		OA1:sr_x = 1 ;
		OA1:sr_y = 1 ;
	float OA2(Time, south_north, west_east) ;
		OA2:FieldType = 104 ;
		OA2:MemoryOrder = "XY " ;
		OA2:units = "whoknows" ;
		OA2:description = "something" ;
		OA2:stagger = "M" ;
		OA2:sr_x = 1 ;
		OA2:sr_y = 1 ;
	float OA3(Time, south_north, west_east) ;
		OA3:FieldType = 104 ;
		OA3:MemoryOrder = "XY " ;
		OA3:units = "whoknows" ;
		OA3:description = "something" ;
		OA3:stagger = "M" ;
		OA3:sr_x = 1 ;
		OA3:sr_y = 1 ;
	float OA4(Time, south_north, west_east) ;
		OA4:FieldType = 104 ;
		OA4:MemoryOrder = "XY " ;
		OA4:units = "whoknows" ;
		OA4:description = "something" ;
		OA4:stagger = "M" ;
		OA4:sr_x = 1 ;
		OA4:sr_y = 1 ;
	float OL1(Time, south_north, west_east) ;
		OL1:FieldType = 104 ;
		OL1:MemoryOrder = "XY " ;
		OL1:units = "whoknows" ;
		OL1:description = "something" ;
		OL1:stagger = "M" ;
		OL1:sr_x = 1 ;
		OL1:sr_y = 1 ;
	float OL2(Time, south_north, west_east) ;
		OL2:FieldType = 104 ;
		OL2:MemoryOrder = "XY " ;
		OL2:units = "whoknows" ;
		OL2:description = "something" ;
		OL2:stagger = "M" ;
		OL2:sr_x = 1 ;
		OL2:sr_y = 1 ;
	float OL3(Time, south_north, west_east) ;
		OL3:FieldType = 104 ;
		OL3:MemoryOrder = "XY " ;
		OL3:units = "whoknows" ;
		OL3:description = "something" ;
		OL3:stagger = "M" ;
		OL3:sr_x = 1 ;
		OL3:sr_y = 1 ;
	float OL4(Time, south_north, west_east) ;
		OL4:FieldType = 104 ;
		OL4:MemoryOrder = "XY " ;
		OL4:units = "whoknows" ;
		OL4:description = "something" ;
		OL4:stagger = "M" ;
		OL4:sr_x = 1 ;
		OL4:sr_y = 1 ;
	float SCB_DOM(Time, south_north, west_east) ;
		SCB_DOM:FieldType = 104 ;
		SCB_DOM:MemoryOrder = "XY " ;
		SCB_DOM:description = "Dominant category" ;
		SCB_DOM:grid_mapping = "lambert_conformal_conic" ;
		SCB_DOM:sr_x = 1 ;
		SCB_DOM:sr_y = 1 ;
		SCB_DOM:stagger = "M" ;
		SCB_DOM:units = "category" ;
	float SCT_DOM(Time, south_north, west_east) ;
		SCT_DOM:FieldType = 104 ;
		SCT_DOM:MemoryOrder = "XY " ;
		SCT_DOM:description = "Dominant category" ;
		SCT_DOM:grid_mapping = "lambert_conformal_conic" ;
		SCT_DOM:sr_x = 1 ;
		SCT_DOM:sr_y = 1 ;
		SCT_DOM:stagger = "M" ;
		SCT_DOM:units = "category" ;
	float SINALPHA(Time, south_north, west_east) ;
		SINALPHA:FieldType = 104 ;
		SINALPHA:MemoryOrder = "XY " ;
		SINALPHA:units = "none" ;
		SINALPHA:description = "Sine of rotation angle" ;
		SINALPHA:stagger = "M" ;
		SINALPHA:sr_x = 1 ;
		SINALPHA:sr_y = 1 ;
	float SLOPECAT(Time, south_north, west_east) ;
		SLOPECAT:FieldType = 104 ;
		SLOPECAT:MemoryOrder = "XY " ;
		SLOPECAT:units = "category" ;
		SLOPECAT:description = "Dominant category" ;
		SLOPECAT:stagger = "M" ;
		SLOPECAT:sr_x = 1 ;
		SLOPECAT:sr_y = 1 ;
	float SNOALB(Time, south_north, west_east) ;
		SNOALB:FieldType = 104 ;
		SNOALB:MemoryOrder = "XY " ;
		SNOALB:units = "percent" ;
		SNOALB:description = "Maximum snow albedo" ;
		SNOALB:stagger = "M" ;
		SNOALB:sr_x = 1 ;
		SNOALB:sr_y = 1 ;
	float SOILCBOT(Time, soil_cat, south_north, west_east) ;
		SOILCBOT:FieldType = 104 ;
		SOILCBOT:MemoryOrder = "XYZ" ;
		SOILCBOT:units = "category" ;
		SOILCBOT:description = "16-category bottom-layer soil type" ;
		SOILCBOT:stagger = "M" ;
		SOILCBOT:sr_x = 1 ;
		SOILCBOT:sr_y = 1 ;
	float SOILCTOP(Time, soil_cat, south_north, west_east) ;
		SOILCTOP:FieldType = 104 ;
		SOILCTOP:MemoryOrder = "XYZ" ;
		SOILCTOP:units = "category" ;
		SOILCTOP:description = "16-category top-layer soil type" ;
		SOILCTOP:stagger = "M" ;
		SOILCTOP:sr_x = 1 ;
		SOILCTOP:sr_y = 1 ;
	float SOILTEMP(Time, south_north, west_east) ;
		SOILTEMP:FieldType = 104 ;
		SOILTEMP:MemoryOrder = "XY " ;
		SOILTEMP:units = "Kelvin" ;
		SOILTEMP:description = "Annual mean deep soil temperature" ;
		SOILTEMP:stagger = "M" ;
		SOILTEMP:sr_x = 1 ;
		SOILTEMP:sr_y = 1 ;
	char Times(Time, DateStrLen) ;
	float VAR(Time, south_north, west_east) ;
		VAR:FieldType = 104 ;
		VAR:MemoryOrder = "XY " ;
		VAR:units = "whoknows" ;
		VAR:description = "something" ;
		VAR:stagger = "M" ;
		VAR:sr_x = 1 ;
		VAR:sr_y = 1 ;
	float VAR_SSO(Time, south_north, west_east) ;
		VAR_SSO:FieldType = 104 ;
		VAR_SSO:MemoryOrder = "XY " ;
		VAR_SSO:units = "meters2 MSL" ;
		VAR_SSO:description = "Variance of Subgrid Scale Orography" ;
		VAR_SSO:stagger = "M" ;
		VAR_SSO:sr_x = 1 ;
		VAR_SSO:sr_y = 1 ;
	float XLAT_C(Time, south_north_stag, west_east_stag) ;
		XLAT_C:FieldType = 104 ;
		XLAT_C:MemoryOrder = "XY " ;
		XLAT_C:units = "degrees latitude" ;
		XLAT_C:description = "Latitude at grid cell corners" ;
		XLAT_C:stagger = "CORNER" ;
		XLAT_C:sr_x = 1 ;
		XLAT_C:sr_y = 1 ;
	float XLAT_M(Time, south_north, west_east) ;
		XLAT_M:FieldType = 104 ;
		XLAT_M:MemoryOrder = "XY " ;
		XLAT_M:units = "degrees latitude" ;
		XLAT_M:description = "Latitude on mass grid" ;
		XLAT_M:stagger = "M" ;
		XLAT_M:sr_x = 1 ;
		XLAT_M:sr_y = 1 ;
	float XLAT_U(Time, south_north, west_east_stag) ;
		XLAT_U:FieldType = 104 ;
		XLAT_U:MemoryOrder = "XY " ;
		XLAT_U:units = "degrees latitude" ;
		XLAT_U:description = "Latitude on U grid" ;
		XLAT_U:stagger = "U" ;
		XLAT_U:sr_x = 1 ;
		XLAT_U:sr_y = 1 ;
	float XLAT_V(Time, south_north_stag, west_east) ;
		XLAT_V:FieldType = 104 ;
		XLAT_V:MemoryOrder = "XY " ;
		XLAT_V:units = "degrees latitude" ;
		XLAT_V:description = "Latitude on V grid" ;
		XLAT_V:stagger = "V" ;
		XLAT_V:sr_x = 1 ;
		XLAT_V:sr_y = 1 ;
	float XLONG_C(Time, south_north_stag, west_east_stag) ;
		XLONG_C:FieldType = 104 ;
		XLONG_C:MemoryOrder = "XY " ;
		XLONG_C:units = "degrees longitude" ;
		XLONG_C:description = "Longitude at grid cell corners" ;
		XLONG_C:stagger = "CORNER" ;
		XLONG_C:sr_x = 1 ;
		XLONG_C:sr_y = 1 ;
	float XLONG_M(Time, south_north, west_east) ;
		XLONG_M:FieldType = 104 ;
		XLONG_M:MemoryOrder = "XY " ;
		XLONG_M:units = "degrees longitude" ;
		XLONG_M:description = "Longitude on mass grid" ;
		XLONG_M:stagger = "M" ;
		XLONG_M:sr_x = 1 ;
		XLONG_M:sr_y = 1 ;
	float XLONG_U(Time, south_north, west_east_stag) ;
		XLONG_U:FieldType = 104 ;
		XLONG_U:MemoryOrder = "XY " ;
		XLONG_U:units = "degrees longitude" ;
		XLONG_U:description = "Longitude on U grid" ;
		XLONG_U:stagger = "U" ;
		XLONG_U:sr_x = 1 ;
		XLONG_U:sr_y = 1 ;
	float XLONG_V(Time, south_north_stag, west_east) ;
		XLONG_V:FieldType = 104 ;
		XLONG_V:MemoryOrder = "XY " ;
		XLONG_V:units = "degrees longitude" ;
		XLONG_V:description = "Longitude on V grid" ;
		XLONG_V:stagger = "V" ;
		XLONG_V:sr_x = 1 ;
		XLONG_V:sr_y = 1 ;

// global attributes:
		:TITLE = "OUTPUT FROM GEOGRID V3.6" ;
		:SIMULATION_START_DATE = "0000-00-00_00:00:00" ;
		:WEST-EAST_GRID_DIMENSION = 16 ;
		:SOUTH-NORTH_GRID_DIMENSION = 17 ;
		:BOTTOM-TOP_GRID_DIMENSION = 0 ;
		:WEST-EAST_PATCH_START_UNSTAG = 1 ;
		:WEST-EAST_PATCH_END_UNSTAG = 15 ;
		:SOUTH-NORTH_PATCH_START_UNSTAG = 1 ;
		:SOUTH-NORTH_PATCH_END_UNSTAG = 16 ;
		:GRIDTYPE = "C" ;
		:DX = 1000.f ;
		:DY = 1000.f ;
		:DYN_OPT = 2 ;
		:CEN_LAT = 40.00001f ;
		:CEN_LON = -97.f ;
		:TRUELAT1 = 30.f ;
		:TRUELAT2 = 60.f ;
		:MOAD_CEN_LAT = 40.00001f ;
		:STAND_LON = -97.f ;
		:POLE_LAT = 90.f ;
		:POLE_LON = 0.f ;
		:corner_lats = 41.42281f, 41.55636f, 41.51907f, 41.3856f, 41.42413f, 41.55769f, 41.51774f, 41.38428f, 41.41836f, 41.56082f, 41.52353f, 41.38115f, 41.41969f, 41.56216f, 41.5222f, 41.37983f ;
		:corner_lons = -73.85333f, -73.80026f, -73.63379f, -73.68719f, -73.85928f, -73.80621f, -73.62784f, -73.68127f, -73.8551f, -73.79849f, -73.63202f, -73.689f, -73.86105f, -73.80444f, -73.62604f, -73.68304f ;
		:MAP_PROJ = 1 ;
		:MMINLU = "USGS" ;
		:NUM_LAND_CAT = 24 ;
		:ISWATER = 16 ;
		:ISLAKE = -1 ;
		:ISICE = 24 ;
		:ISURBAN = 1 ;
		:ISOILWATER = 14 ;
		:grid_id = 1 ;
		:parent_id = 1 ;
		:i_parent_start = 1 ;
		:j_parent_start = 1 ;
		:i_parent_end = 16 ;
		:j_parent_end = 17 ;
		:parent_grid_ratio = 1 ;
		:sr_x = 1 ;
		:sr_y = 1 ;
		:FLAG_MF_XY = 1 ;
		:FLAG_LAI12M = 1 ;
		:FLAG_LAKE_DEPTH = 1 ;
		:NCO = "netCDF Operators version 4.7.4 (http://nco.sf.net)" ;
		:nco_openmp_thread_number = 1 ;
		:history = "Fri Aug 24 11:14:03 2018: ncatted -O -a corner_lats,global,o,f,41.4228134155273,41.5563621520996,41.5190696716309,41.3856048583984,41.4241333007812,41.5576934814453,41.5177383422852,41.38427734375,41.4183578491211,41.5608215332031,41.5235252380371,41.3811531066895,41.4196891784668,41.5621604919434,41.5221977233887,41.3798332214355 /glade/scratch/adugger/TestCases/NY_Croton/DOMAIN_NWMv2.0//0137462010/geo_em.d0x.nc\n",
			"Fri Aug 24 11:14:03 2018: ncatted -O -a corner_lons,global,o,f,-73.8533325195312,-73.8002624511719,-73.6337890625,-73.6871948242188,-73.8592834472656,-73.8062133789062,-73.6278381347656,-73.6812744140625,-73.8551025390625,-73.7984924316406,-73.6320190429688,-73.6889953613281,-73.8610534667969,-73.804443359375,-73.6260375976562,-73.6830444335938 /glade/scratch/adugger/TestCases/NY_Croton/DOMAIN_NWMv2.0//0137462010/geo_em.d0x.nc\n",
			"Fri Aug 24 11:13:56 2018: ncks -d west_east,4146,4160 -d south_north,2341,2356 -d west_east_stag,4146,4161 -d south_north_stag,2341,2357 /glade/p_old/nwc/nwmv20_finals/CONUS/DOMAIN/geo_em.d01.conus_1km_NWMv2.0_XLATC_XLONC.nc /glade/scratch/adugger/TestCases/NY_Croton/DOMAIN_NWMv2.0//0137462010/geo_em.d0x.nc\n",
			"Wed Aug 22 18:05:18 2018: ncks -A -v XLONG_C ../../../nwmv12_finals/DOMAIN/geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix_XLATC_XLONC geo_em.d01.conus_1km_NWMv2.0_XLATC_XLONC.nc\n",
			"Wed Aug 22 18:04:49 2018: ncks -A -v XLAT_C ../../../nwmv12_finals/DOMAIN/geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix_XLATC_XLONC geo_em.d01.conus_1km_NWMv2.0_XLATC_XLONC.nc" ;
		:history_of_appended_files = "Wed Aug 22 18:05:18 2018: Appended file ../../../nwmv12_finals/DOMAIN/geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix_XLATC_XLONC had following \"history\" attribute:\n",
			"Fri Dec 29 11:17:44 2017: ncks -A -v XLONG_C /glade/scratch/barlage/nwm/geo_em.d01.nc geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix_XLATC_XLONC\n",
			"Fri Dec 29 11:16:15 2017: ncks -A -v XLAT_C /glade/scratch/barlage/nwm/geo_em.d01.nc geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix_XLATC_XLONC\n",
			"Sat Apr 29 22:02:59 2017: ncks -A -v LANDUSEF landusef_new.nc geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix\n",
			"Sat Apr 29 22:02:12 2017: ncks -A -v SOILCTOP soilctop_new.nc geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix\n",
			"Sat Apr 29 21:59:20 2017: ncks -x -v SOILCTOP,LANDUSEF geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix\n",
			"Sat Dec  5 16:05:33 2015: ncap2 -s where(LU_INDEX==24) LU_INDEX=23 geo_em.d01.nc.conus_1km_nlcd11 geo_em.d01.nc.conus_1km_nlcd11_glacfix\n",
			"Wed Dec  2 17:47:01 2015: ncap2 -s where(LU_INDEX==16) LANDMASK=0; elsewhere LANDMASK=1 geo_em.d01.nc.conus_1km_nlcd11 geo_em.d01.nc.conus_1km_nlcd11a\n",
			"Wed Dec  2 17:39:34 2015: ncks -A -v LU_INDEX out_tmp2.nc geo_em.d01.nc.conus_1km_nlcd11\n",
			"Wed Aug 22 18:04:49 2018: Appended file ../../../nwmv12_finals/DOMAIN/geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix_XLATC_XLONC had following \"history\" attribute:\n",
			"Fri Dec 29 11:17:44 2017: ncks -A -v XLONG_C /glade/scratch/barlage/nwm/geo_em.d01.nc geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix_XLATC_XLONC\n",
			"Fri Dec 29 11:16:15 2017: ncks -A -v XLAT_C /glade/scratch/barlage/nwm/geo_em.d01.nc geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix_XLATC_XLONC\n",
			"Sat Apr 29 22:02:59 2017: ncks -A -v LANDUSEF landusef_new.nc geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix\n",
			"Sat Apr 29 22:02:12 2017: ncks -A -v SOILCTOP soilctop_new.nc geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix\n",
			"Sat Apr 29 21:59:20 2017: ncks -x -v SOILCTOP,LANDUSEF geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix geo_em.d01.nc.conus_1km_nlcd11_glacfix_soilctopfix\n",
			"Sat Dec  5 16:05:33 2015: ncap2 -s where(LU_INDEX==24) LU_INDEX=23 geo_em.d01.nc.conus_1km_nlcd11 geo_em.d01.nc.conus_1km_nlcd11_glacfix\n",
			"Wed Dec  2 17:47:01 2015: ncap2 -s where(LU_INDEX==16) LANDMASK=0; elsewhere LANDMASK=1 geo_em.d01.nc.conus_1km_nlcd11 geo_em.d01.nc.conus_1km_nlcd11a\n",
			"Wed Dec  2 17:39:34 2015: ncks -A -v LU_INDEX out_tmp2.nc geo_em.d01.nc.conus_1km_nlcd11\n",
			"" ;
}
