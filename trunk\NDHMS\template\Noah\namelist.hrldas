&NOAHLSM_OFFLINE

HRLDAS_CONSTANTS_FILE = "DOMAIN/wrfinput_d01.nc"
INDIR  = "./FORCING"
OUTDIR = "."

START_YEAR   = 2013
START_MONTH  = 09
START_DAY    = 11
START_HOUR   = 00
START_MIN    = 00

! Specification of the land surface model restart file
! Comment out the option if not initializing from a restart file
RESTART_FILENAME_REQUESTED = "RESTART/RESTART.2011082600_DOMAIN1"

! Specification of simulation length in hours OR days
! KDAY = 7 ! This option is deprecated and may be removed in a future version
KHOUR = 24

! Timesteps in units of seconds
FORCING_TIMESTEP = 3600
NOAH_TIMESTEP    = 3600
OUTPUT_TIMESTEP  = 3600

! Land surface model restart file write frequency
! A value of -99999 will output restarts on the first day of the month only
RESTART_FREQUENCY_HOURS = 24

! Split output after split_output_count output times.
SPLIT_OUTPUT_COUNT = 1

! Soil layer specification
NSOIL=4
ZSOIL(1) = -0.10
ZSOIL(2) = -0.40
ZSOIL(3) = -1.00
ZSOIL(4) = -2.00

! Forcing data measurement heights
ZLVL = 2.0       ! for temp and humidity
ZLVL_WIND = 10.0 ! for wind variables

IZ0TLND = 0
SFCDIF_OPTION = 0
UPDATE_SNOW_FROM_FORCING = .FALSE.

! Specification of forcing data:  1=HRLDAS-hr format, 2=HRLDAS-min format, 3=WRF,
!    4=Idealized, 5=Idealized w/ spec. precip.,
!    6=HRLDAS-hr format w/ spec. precip., 7=WRF w/ spec. precip.,
FORC_TYP = 1

/
