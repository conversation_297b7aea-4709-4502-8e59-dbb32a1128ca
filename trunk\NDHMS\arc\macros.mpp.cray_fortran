.IGNORE:

ifeq ($(SPATIAL_SOIL),1)
SPATIAL_SOIL = -DSPATIAL_SOIL
else
SPATIAL_SOIL = 
endif

ifeq ($(HYDRO_REALTIME),1)
HYDRO_REALTIME = -DHYDRO_REALTIME
else
HYDRO_REALTIME =
endif

ifeq ($(WRF_HYDRO),1)
WRF_HYDRO = -DWRF_HYDRO $(HYDRO_REALTIME)
else
WRF_HYDRO =
endif

ifeq ($(WRF_HYDRO_RAPID),1)
WRF_HYDRO = -DWRF_HYDRO -DWRF_HYDRO_RAPID $(HYDRO_REALTIME)
endif

ifeq ($(HYDRO_D),1)
HYDRO_D = -DHYDRO_D $(WRF_HYDRO)
else
HYDRO_D =  $(WRF_HYDRO)
endif


ifeq ($(WRF_HYDRO_NUDGING),1)
WRF_HYDRO_NUDGING = -DWRF_HYDRO_NUDGING
else
WRF_HYDRO_NUDGING = 
endif

ifeq ($(OUTPUT_CHAN_CONN),1)
OUTPUT_CHAN_CONN = -DOUTPUT_CHAN_CONN
else
OUTPUT_CHAN_CONN = 
endif

ifeq ($(PRECIP_DOUBLE),1)
PRECIP_DOUBLE = -DPRECIP_DOUBLE
else
PRECIP_DOUBLE = 
endif

ifeq ($(NWM_META),1)
NWM_META = -DNWM_META
else
NWM_META =
endif

ifeq ($(NCEP_WCOSS),1)
NCEP_WCOSS = -DNCEP_WCOSS
else
NCEP_WCOSS =
endif

RMD	        = rm -f
COMPILER90  = ftn
FORMAT_FREE = -f free
BYTESWAPIO  = -h byteswapio
F90FLAGS    = -O2 -c -ef -h alias=none -h fp1 $(FORMAT_FREE) $(BYTESWAPIO)
#F90FLAGS    = -O0 -eD -g -c -ef -h alias=tolerant $(FORMAT_FREE) $(BYTESWAPIO)   # CRAY DEBUG OPTIONS
MODFLAG     = -I./ -I ../../MPP -I ../MPP -I ../mod
LDFLAGS     =
CPPINVOKE   = -eT
CPPFLAGS    = -DMPP_LAND -I ../Data_Rec $(HYDRO_D) $(SPATIAL_SOIL) $(NWM_META) $(WRF_HYDRO_NUDGING) $(OUTPUT_CHAN_CONN) $(PRECIP_DOUBLE) $(NCEP_WCOSS)
LIBS 	    =	
NETCDFINC   = $(NETCDF_INC)
NETCDFLIB   = -L$(NETCDF_LIB) -lnetcdff -lnetcdf
