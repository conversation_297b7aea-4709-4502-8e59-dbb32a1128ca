.IGNORE:

ifeq ($(HYDRO_REALTIME),1)
HY<PERSON>O_REALTIME = -DHYDRO_REALTIME
else
HYDRO_REALTIME =
endif

ifeq ($(WRF_HYDRO),1)
WRF_HYDRO = -DWRF_HYDRO $(HYDRO_REALTIME)
else
WRF_HYDRO =
endif

ifeq ($(WRF_HYDRO_RAPID),1)
WRF_HYDRO = -DWRF_HYDRO -DWRF_HYDRO_RAPID $(HYDRO_REALTIME)
endif

ifeq ($(HYDRO_D),1)
HYDRO_D = -DHYDRO_D $(WRF_HYDRO)
else
HYDRO_D =  $(WRF_HYDRO)
endif



RMD             = rm -f
COMPILER90      = ftn
FORMAT_FREE     = -FR
BYTESWAPIO      = -convert big_endian
F90FLAGS        = -w -c -ftz -align all -fno-alias -fp-model precise $(FORMAT_FREE) $(BYTESWAPIO)
MODFLAG         = -I./ -I../../MPP -I../MPP -I../mod
LDFLAGS         =
CPPINVOKE       = -fpp
CPPFLAGS        = -DMPP_LAND -I../Data_Rec $(HYDRO_D) 
LIBS            =
NETCDFINC       = $(NETCDF_INC)
NETCDFLIB       = -L$(NETCDF_LIB) -lnetcdff -lnetcdf
NETCDF_INC = /opt/cray/netcdf-hdf5parallel/4.3.2/INTEL/140/include
NETCDF_LIB = /opt/cray/netcdf-hdf5parallel/4.3.2/INTEL/140/lib
