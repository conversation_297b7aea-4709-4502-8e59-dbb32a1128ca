!*******************************************************************************
!Subroutine - rapid_open_Vlat_file
!*******************************************************************************
subroutine rapid_open_Vlat_file(Vlat_file) 

!Purpose:
!Open Vlat_file from Fortran/netCDF.
!Author: 
!Cedric H. David, 2013-2015.


!*******************************************************************************
!Global variables
!*******************************************************************************
use netcdf
use rapid_var, only :                                                          &
                   rank,IS_nc_status,IS_nc_id_fil_Vlat,IS_nc_id_var_Vlat

implicit none


!*******************************************************************************
!Includes
!*******************************************************************************


!*******************************************************************************
!Intent (in/out), and local variables 
!*******************************************************************************
character(len=100), intent(in):: Vlat_file


!*******************************************************************************
!Open file
!*******************************************************************************
if (rank==0) then 
     open(99,file=Vlat_file,status='old')
     close(99)
     IS_nc_status=NF90_OPEN(Vlat_file,NF90_NOWRITE,IS_nc_id_fil_Vlat)
     IS_nc_status=NF90_INQ_VARID(IS_nc_id_fil_Vlat,'m3_riv',IS_nc_id_var_Vlat)
end if


!*******************************************************************************
!End 
!*******************************************************************************

end subroutine rapid_open_Vlat_file

