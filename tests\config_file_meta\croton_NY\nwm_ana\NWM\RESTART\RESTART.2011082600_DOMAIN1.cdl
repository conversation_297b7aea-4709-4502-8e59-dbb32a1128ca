netcdf RESTART {
dimensions:
	Time = UNLIMITED ; // (1 currently)
	DateStrLen = 19 ;
	west_east = 15 ;
	south_north = 16 ;
	west_east_stag = 16 ;
	south_north_stag = 17 ;
	soil_layers_stag = 4 ;
	snow_layers = 3 ;
	sosn_layers = 7 ;
variables:
	char Times(Time, DateStrLen) ;
	float SOIL_T(Time, south_north, soil_layers_stag, west_east) ;
		SOIL_T:MemoryOrder = "XZY" ;
		SOIL_T:description = "" ;
		SOIL_T:units = "" ;
	float SNOW_T(Time, south_north, snow_layers, west_east) ;
		SNOW_T:MemoryOrder = "XZY" ;
		SNOW_T:description = "" ;
		SNOW_T:units = "" ;
	float SMC(Time, south_north, soil_layers_stag, west_east) ;
		SMC:MemoryOrder = "XZY" ;
		SMC:description = "" ;
		SMC:units = "" ;
	float SH2O(Time, south_north, soil_layers_stag, west_east) ;
		SH2O:MemoryOrder = "XZY" ;
		SH2O:description = "" ;
		SH2O:units = "" ;
	float ZSNSO(Time, south_north, sosn_layers, west_east) ;
		ZSNSO:MemoryOrder = "XZY" ;
		ZSNSO:description = "" ;
		ZSNSO:units = "" ;
	float SNICE(Time, south_north, snow_layers, west_east) ;
		SNICE:MemoryOrder = "XZY" ;
		SNICE:description = "" ;
		SNICE:units = "" ;
	float SNLIQ(Time, south_north, snow_layers, west_east) ;
		SNLIQ:MemoryOrder = "XZY" ;
		SNLIQ:description = "" ;
		SNLIQ:units = "" ;
	float QSNOW(Time, south_north, west_east) ;
		QSNOW:MemoryOrder = "XY" ;
		QSNOW:description = "" ;
		QSNOW:units = "" ;
	float FWET(Time, south_north, west_east) ;
		FWET:MemoryOrder = "XY" ;
		FWET:description = "" ;
		FWET:units = "" ;
	float SNEQVO(Time, south_north, west_east) ;
		SNEQVO:MemoryOrder = "XY" ;
		SNEQVO:description = "" ;
		SNEQVO:units = "" ;
	float EAH(Time, south_north, west_east) ;
		EAH:MemoryOrder = "XY" ;
		EAH:description = "" ;
		EAH:units = "" ;
	float TAH(Time, south_north, west_east) ;
		TAH:MemoryOrder = "XY" ;
		TAH:description = "" ;
		TAH:units = "" ;
	float ALBOLD(Time, south_north, west_east) ;
		ALBOLD:MemoryOrder = "XY" ;
		ALBOLD:description = "" ;
		ALBOLD:units = "" ;
	float CM(Time, south_north, west_east) ;
		CM:MemoryOrder = "XY" ;
		CM:description = "" ;
		CM:units = "" ;
	float CH(Time, south_north, west_east) ;
		CH:MemoryOrder = "XY" ;
		CH:description = "" ;
		CH:units = "" ;
	int ISNOW(Time, south_north, west_east) ;
		ISNOW:MemoryOrder = "XY" ;
		ISNOW:description = "" ;
		ISNOW:units = "" ;
	float CANLIQ(Time, south_north, west_east) ;
		CANLIQ:MemoryOrder = "XY" ;
		CANLIQ:description = "" ;
		CANLIQ:units = "" ;
	float CANICE(Time, south_north, west_east) ;
		CANICE:MemoryOrder = "XY" ;
		CANICE:description = "" ;
		CANICE:units = "" ;
	float SNEQV(Time, south_north, west_east) ;
		SNEQV:MemoryOrder = "XY" ;
		SNEQV:description = "" ;
		SNEQV:units = "" ;
	float SNOWH(Time, south_north, west_east) ;
		SNOWH:MemoryOrder = "XY" ;
		SNOWH:description = "" ;
		SNOWH:units = "" ;
	float TV(Time, south_north, west_east) ;
		TV:MemoryOrder = "XY" ;
		TV:description = "" ;
		TV:units = "" ;
	float TG(Time, south_north, west_east) ;
		TG:MemoryOrder = "XY" ;
		TG:description = "" ;
		TG:units = "" ;
	float ZWT(Time, south_north, west_east) ;
		ZWT:MemoryOrder = "XY" ;
		ZWT:description = "" ;
		ZWT:units = "" ;
	float WA(Time, south_north, west_east) ;
		WA:MemoryOrder = "XY" ;
		WA:description = "" ;
		WA:units = "" ;
	float WT(Time, south_north, west_east) ;
		WT:MemoryOrder = "XY" ;
		WT:description = "" ;
		WT:units = "" ;
	float WSLAKE(Time, south_north, west_east) ;
		WSLAKE:MemoryOrder = "XY" ;
		WSLAKE:description = "" ;
		WSLAKE:units = "" ;
	float LFMASS(Time, south_north, west_east) ;
		LFMASS:MemoryOrder = "XY" ;
		LFMASS:description = "" ;
		LFMASS:units = "" ;
	float RTMASS(Time, south_north, west_east) ;
		RTMASS:MemoryOrder = "XY" ;
		RTMASS:description = "" ;
		RTMASS:units = "" ;
	float STMASS(Time, south_north, west_east) ;
		STMASS:MemoryOrder = "XY" ;
		STMASS:description = "" ;
		STMASS:units = "" ;
	float WOOD(Time, south_north, west_east) ;
		WOOD:MemoryOrder = "XY" ;
		WOOD:description = "" ;
		WOOD:units = "" ;
	float STBLCP(Time, south_north, west_east) ;
		STBLCP:MemoryOrder = "XY" ;
		STBLCP:description = "" ;
		STBLCP:units = "" ;
	float FASTCP(Time, south_north, west_east) ;
		FASTCP:MemoryOrder = "XY" ;
		FASTCP:description = "" ;
		FASTCP:units = "" ;
	float LAI(Time, south_north, west_east) ;
		LAI:MemoryOrder = "XY" ;
		LAI:description = "" ;
		LAI:units = "" ;
	float SAI(Time, south_north, west_east) ;
		SAI:MemoryOrder = "XY" ;
		SAI:description = "" ;
		SAI:units = "" ;
	float VEGFRA(Time, south_north, west_east) ;
		VEGFRA:MemoryOrder = "XY" ;
		VEGFRA:description = "" ;
		VEGFRA:units = "" ;
	float GVFMIN(Time, south_north, west_east) ;
		GVFMIN:MemoryOrder = "XY" ;
		GVFMIN:description = "" ;
		GVFMIN:units = "" ;
	float GVFMAX(Time, south_north, west_east) ;
		GVFMAX:MemoryOrder = "XY" ;
		GVFMAX:description = "" ;
		GVFMAX:units = "" ;
	float ACMELT(Time, south_north, west_east) ;
		ACMELT:MemoryOrder = "XY" ;
		ACMELT:description = "" ;
		ACMELT:units = "" ;
	float ACSNOW(Time, south_north, west_east) ;
		ACSNOW:MemoryOrder = "XY" ;
		ACSNOW:description = "" ;
		ACSNOW:units = "" ;
	float TAUSS(Time, south_north, west_east) ;
		TAUSS:MemoryOrder = "XY" ;
		TAUSS:description = "" ;
		TAUSS:units = "" ;
	float QSFC(Time, south_north, west_east) ;
		QSFC:MemoryOrder = "XY" ;
		QSFC:description = "" ;
		QSFC:units = "" ;
	float SFCRUNOFF(Time, south_north, west_east) ;
		SFCRUNOFF:MemoryOrder = "XY" ;
		SFCRUNOFF:description = "" ;
		SFCRUNOFF:units = "" ;
	float UDRUNOFF(Time, south_north, west_east) ;
		UDRUNOFF:MemoryOrder = "XY" ;
		UDRUNOFF:description = "" ;
		UDRUNOFF:units = "" ;
	float ACCPRCP(Time, south_north, west_east) ;
		ACCPRCP:MemoryOrder = "XY" ;
		ACCPRCP:description = "" ;
		ACCPRCP:units = "" ;
	float ACCECAN(Time, south_north, west_east) ;
		ACCECAN:MemoryOrder = "XY" ;
		ACCECAN:description = "" ;
		ACCECAN:units = "" ;
	float ACCEDIR(Time, south_north, west_east) ;
		ACCEDIR:MemoryOrder = "XY" ;
		ACCEDIR:description = "" ;
		ACCEDIR:units = "" ;
	float ACCETRAN(Time, south_north, west_east) ;
		ACCETRAN:MemoryOrder = "XY" ;
		ACCETRAN:description = "" ;
		ACCETRAN:units = "" ;
	float SMOISEQ(Time, south_north, soil_layers_stag, west_east) ;
		SMOISEQ:MemoryOrder = "XZY" ;
		SMOISEQ:description = "" ;
		SMOISEQ:units = "" ;
	float AREAXY(Time, south_north, west_east) ;
		AREAXY:MemoryOrder = "XY" ;
		AREAXY:description = "" ;
		AREAXY:units = "" ;
	float SMCWTDXY(Time, south_north, west_east) ;
		SMCWTDXY:MemoryOrder = "XY" ;
		SMCWTDXY:description = "" ;
		SMCWTDXY:units = "" ;
	float DEEPRECHXY(Time, south_north, west_east) ;
		DEEPRECHXY:MemoryOrder = "XY" ;
		DEEPRECHXY:description = "" ;
		DEEPRECHXY:units = "" ;
	float QSLATXY(Time, south_north, west_east) ;
		QSLATXY:MemoryOrder = "XY" ;
		QSLATXY:description = "" ;
		QSLATXY:units = "" ;
	float QRFSXY(Time, south_north, west_east) ;
		QRFSXY:MemoryOrder = "XY" ;
		QRFSXY:description = "" ;
		QRFSXY:units = "" ;
	float QSPRINGSXY(Time, south_north, west_east) ;
		QSPRINGSXY:MemoryOrder = "XY" ;
		QSPRINGSXY:description = "" ;
		QSPRINGSXY:units = "" ;
	float RECHXY(Time, south_north, west_east) ;
		RECHXY:MemoryOrder = "XY" ;
		RECHXY:description = "" ;
		RECHXY:units = "" ;
	float QRFXY(Time, south_north, west_east) ;
		QRFXY:MemoryOrder = "XY" ;
		QRFXY:description = "" ;
		QRFXY:units = "" ;
	float QSPRINGXY(Time, south_north, west_east) ;
		QSPRINGXY:MemoryOrder = "XY" ;
		QSPRINGXY:description = "" ;
		QSPRINGXY:units = "" ;
	float FDEPTHXY(Time, south_north, west_east) ;
		FDEPTHXY:MemoryOrder = "XY" ;
		FDEPTHXY:description = "" ;
		FDEPTHXY:units = "" ;
	float RIVERCONDXY(Time, south_north, west_east) ;
		RIVERCONDXY:MemoryOrder = "XY" ;
		RIVERCONDXY:description = "" ;
		RIVERCONDXY:units = "" ;
	float RIVERBEDXY(Time, south_north, west_east) ;
		RIVERBEDXY:MemoryOrder = "XY" ;
		RIVERBEDXY:description = "" ;
		RIVERBEDXY:units = "" ;
	float EQZWT(Time, south_north, west_east) ;
		EQZWT:MemoryOrder = "XY" ;
		EQZWT:description = "" ;
		EQZWT:units = "" ;
	float PEXPXY(Time, south_north, west_east) ;
		PEXPXY:MemoryOrder = "XY" ;
		PEXPXY:description = "" ;
		PEXPXY:units = "" ;

// global attributes:
		:TITLE = "RESTART FILE FROM HRLDAS v20150506" ;
		:missing_value = -1.e+33f ;
		:START_DATE = "2011-07-01_00:00:00" ;
		:MAP_PROJ = 1 ;
		:LAT1 = 41.42281f ;
		:LON1 = -73.85333f ;
		:DX = 1000.f ;
		:DY = 1000.f ;
		:TRUELAT1 = 30.f ;
		:TRUELAT2 = 60.f ;
		:STAND_LON = -97.f ;
		:MMINLU = "USGS" ;
}
