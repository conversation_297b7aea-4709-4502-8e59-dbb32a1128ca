!*******************************************************************************
!Subroutine - rapid_close_Qout_file 
!*******************************************************************************
subroutine rapid_close_Qout_file

!Purpose:
!Close Qout_file from Fortran/netCDF.
!Author: 
!Cedric H. David, 2013-2015.


!*******************************************************************************
!Global variables
!*******************************************************************************
use netcdf
use rapid_var, only :                                                          &
                   rank,IS_nc_status,IS_nc_id_fil_Qout


implicit none


!*******************************************************************************
!Intent (in/out), and local variables 
!*******************************************************************************


!*******************************************************************************
!Includes
!*******************************************************************************


!*******************************************************************************
!Close file
!*******************************************************************************
if (rank==0) IS_nc_status=NF90_CLOSE(IS_nc_id_fil_Qout)


!*******************************************************************************
!End subroutine
!*******************************************************************************
end subroutine rapid_close_Qout_file
